<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Replace Professor Photo - <PERSON><PERSON> <PERSON></title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 2rem;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.2);
        }
        h1 {
            text-align: center;
            margin-bottom: 2rem;
            font-size: 2.5rem;
        }
        .current-photo {
            text-align: center;
            margin: 2rem 0;
        }
        .current-photo img {
            width: 200px;
            height: 200px;
            border-radius: 50%;
            object-fit: cover;
            border: 4px solid white;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        .instructions {
            background: rgba(255, 255, 255, 0.1);
            padding: 1.5rem;
            border-radius: 15px;
            margin: 2rem 0;
        }
        .step {
            margin: 1rem 0;
            padding: 1rem;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border-left: 4px solid #4caf50;
        }
        .file-path {
            background: rgba(0, 0, 0, 0.3);
            padding: 0.5rem;
            border-radius: 5px;
            font-family: monospace;
            margin: 0.5rem 0;
            word-break: break-all;
        }
        .note {
            background: rgba(255, 193, 7, 0.2);
            border: 1px solid rgba(255, 193, 7, 0.5);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
        .success {
            background: rgba(76, 175, 80, 0.2);
            border: 1px solid rgba(76, 175, 80, 0.5);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎓 Replace Professor Photo</h1>
        
        <div class="current-photo">
            <h3>Current Photo:</h3>
            <img src="public/images/hymavathi.jpg" alt="Current Professor Photo" onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjY3ZWVhIiByeD0iMTAwIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSI0MCI+8J+RqeKAjfCfj6s8L3RleHQ+Cjx0ZXh0IHg9IjEwMCIgeT0iMTQwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIxMiI+UGhvdG8gTm90IEZvdW5kPC90ZXh0Pgo8L3N2Zz4K'">
        </div>

        <div class="instructions">
            <h3>📋 How to Replace Your Photo:</h3>
            
            <div class="step">
                <h4>Step 1: Prepare Your Photo</h4>
                <p>• Use a professional, high-quality photo</p>
                <p>• Recommended size: 400x400 pixels or larger</p>
                <p>• Format: JPG or PNG</p>
                <p>• Make sure it's well-lit and professional</p>
            </div>

            <div class="step">
                <h4>Step 2: Save Your Photo</h4>
                <p>Save your photo to this exact location:</p>
                <div class="file-path">
                    C:\Users\<USER>\OneDrive\Desktop\uspro\Hyma\public\images\hymavathi.jpg
                </div>
                <p><strong>Important:</strong> The filename must be exactly <code>hymavathi.jpg</code></p>
            </div>

            <div class="step">
                <h4>Step 3: Refresh Your Website</h4>
                <p>• Go to your website: <a href="http://localhost:3000" target="_blank" style="color: #4caf50;">http://localhost:3000</a></p>
                <p>• Press <strong>Ctrl + F5</strong> to hard refresh</p>
                <p>• Your photo should now appear!</p>
            </div>
        </div>

        <div class="note">
            <h4>💡 Quick Tip:</h4>
            <p>If your photo doesn't appear immediately, try:</p>
            <ul>
                <li>Clear your browser cache</li>
                <li>Check that the filename is exactly <code>hymavathi.jpg</code></li>
                <li>Make sure the file is in the correct folder</li>
                <li>Restart the development server if needed</li>
            </ul>
        </div>

        <div class="success">
            <h4>✅ Your Photo Will Appear On:</h4>
            <ul>
                <li><strong>Home Page:</strong> Large circular profile photo</li>
                <li><strong>About Page:</strong> Professional portrait</li>
                <li><strong>All Pages:</strong> Consistent branding</li>
            </ul>
        </div>

        <div style="text-align: center; margin-top: 2rem;">
            <p><strong>Your academic portfolio website is ready to showcase your professional image!</strong></p>
            <p>🎓 Ms. Hymavathi Thottathyl - Assistant Professor & PhD Scholar</p>
        </div>
    </div>
</body>
</html>
