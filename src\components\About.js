import React from 'react';
import { motion } from 'framer-motion';
import { FaGraduationCap, FaAward, Fa<PERSON><PERSON><PERSON>, FaBookOpen } from 'react-icons/fa';
import '../styles/About.css';

const About = () => {
  const stats = [
    {
      icon: <FaGraduationCap />,
      number: "17Y 11M",
      label: "Total Experience"
    },
    {
      icon: <FaBookOpen />,
      number: "4",
      label: "Journal Publications"
    },
    {
      icon: <FaAward />,
      number: "1",
      label: "Patent Filed"
    },
    {
      icon: <FaUsers />,
      number: "35",
      label: "Students Guided"
    }
  ];

  const expertise = [
    "Bio-Informatics",
    "Machine Learning",
    "Computer Applications",
    "Breast Cancer Gene Prediction",
    "Microarray Gene Clustering",
    "Differential Evolution Models",
    "DELSTM Models",
    "AI-Driven Personal Assistants"
  ];

  return (
    <section id="about" className="section about">
      <div className="container">
        <motion.h2
          className="section-title"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          About Me
        </motion.h2>

        <div className="about-content">
          <motion.div
            className="about-text"
            initial={{ opacity: 0, x: -50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="about-intro">
              <h3>Dedicated Educator & Research Scholar</h3>
              <p>
                With over 17 years and 11 months of experience in Computer Applications, I am Mrs. Hymavathi Thottathyl,
                an Assistant Professor at R.V.R. & J.C. College of Engineering. My research specializes in Bio-Informatics
                and Machine Learning, particularly in breast cancer gene prediction using advanced computational methods.
              </p>
              <p>
                I am passionate about developing innovative algorithms for microarray gene clustering and creating
                AI-driven solutions that can make a real difference in healthcare and technology. My work includes
                published research in international journals and a filed patent for AI-driven personal assistants.
              </p>
            </div>

            <div className="expertise-section">
              <h4>Areas of Expertise</h4>
              <div className="expertise-grid">
                {expertise.map((skill, index) => (
                  <motion.div
                    key={skill}
                    className="expertise-item"
                    initial={{ opacity: 0, scale: 0.8 }}
                    whileInView={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                  >
                    {skill}
                  </motion.div>
                ))}
              </div>
            </div>
          </motion.div>

          <motion.div
            className="about-image"
            initial={{ opacity: 0, x: 50 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <div className="image-placeholder">
              <div className="placeholder-content">
                <div className="placeholder-icon">🎓</div>
                <p>Academic Portrait</p>
              </div>
            </div>
          </motion.div>
        </div>

        <motion.div
          className="stats-section"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="stats-grid">
            {stats.map((stat, index) => (
              <motion.div
                key={stat.label}
                className="stat-card"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="stat-icon">{stat.icon}</div>
                <div className="stat-number">{stat.number}</div>
                <div className="stat-label">{stat.label}</div>
              </motion.div>
            ))}
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default About;
