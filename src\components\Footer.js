import React from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { <PERSON>aHeart, FaLinkedin, FaTwitter, FaGoogle, FaGraduationCap, FaEnvelope, FaPhone, FaMapMarkerAlt } from 'react-icons/fa';
import '../styles/Footer.css';

const Footer = () => {
  const currentYear = new Date().getFullYear();

  const quickLinks = [
    { name: 'About', href: '/about' },
    { name: 'Research', href: '/research' },
    { name: 'Publications', href: '/publications' },
    { name: 'Teaching', href: '/teaching' },
    { name: 'Contact', href: '/contact' }
  ];

  const socialLinks = [
    {
      icon: <FaLinkedin />,
      name: "LinkedIn",
      url: "https://www.linkedin.com/in/hymavathi-thottathyl-30474625/"
    },
    {
      icon: <FaGoogle />,
      name: "Google Scholar",
      url: "https://scholar.google.com/citations?user=hymavathi"
    },
    {
      icon: <FaGraduationCap />,
      name: "College Profile",
      url: "https://rvrjc.ac.in"
    },
    {
      icon: <FaEnvelope />,
      name: "Email",
      url: "mailto:<EMAIL>"
    }
  ];

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  return (
    <footer className="footer">
      <div className="container">
        <div className="footer-content">
          {/* Logo and Description */}
          <motion.div
            className="footer-section"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            <Link to="/" onClick={scrollToTop}>
              <div className="footer-logo">
                <img
                  src="/images/logo-transparent.svg"
                  alt="Hymavathi - Motivate Educate Elevate"
                  className="footer-logo-image"
                />
              </div>
            </Link>
            <p className="footer-description">
              Dedicated to advancing knowledge through innovative research,
              inspiring education, and meaningful collaboration in the field
              of computer science and engineering. Motivate, Educate, Elevate.
            </p>
            <div className="footer-social">
              {socialLinks.map((social, index) => (
                <motion.a
                  key={social.name}
                  href={social.url}
                  className="footer-social-link"
                  target="_blank"
                  rel="noopener noreferrer"
                  aria-label={social.name}
                  whileHover={{ scale: 1.1, y: -2 }}
                  whileTap={{ scale: 0.95 }}
                >
                  {social.icon}
                </motion.a>
              ))}
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            className="footer-section"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
          >
            <h4>Quick Links</h4>
            <ul className="footer-links">
              {quickLinks.map((link, index) => (
                <li key={link.name}>
                  <Link
                    to={link.href}
                    className="footer-link"
                  >
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </motion.div>

          {/* Contact Info */}
          <motion.div
            className="footer-section"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
          >
            <h4>Contact Info</h4>
            <div className="footer-contact">
              <div className="contact-item">
                <FaEnvelope className="contact-icon" />
                <a href="mailto:<EMAIL>">
                  <EMAIL>
                </a>
              </div>
              <div className="contact-item">
                <FaPhone className="contact-icon" />
                <a href="tel:+************">
                  +91 94910 73317 / 73318
                </a>
              </div>
              <div className="contact-item">
                <FaMapMarkerAlt className="contact-icon" />
                <span>
                  R.V.R. & J.C. College of Engineering<br />
                  Chandramoulipuram, Chowdavaram<br />
                  GUNTUR-522 019, Andhra Pradesh, India
                </span>
              </div>
            </div>
          </motion.div>

          {/* Research Areas */}
          <motion.div
            className="footer-section"
            initial={{ opacity: 0, y: 30 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
            viewport={{ once: true }}
          >
            <h4>Research Areas</h4>
            <ul className="research-areas">
              <li>Computer Science</li>
              <li>Software Engineering</li>
              <li>Data Structures</li>
              <li>Programming Languages</li>
              <li>Educational Technology</li>
            </ul>
          </motion.div>
        </div>

        {/* Footer Bottom */}
        <motion.div
          className="footer-bottom"
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="footer-bottom-content">
            <p className="copyright">
              © {currentYear} Ms. Hymavathi. All rights reserved.
            </p>
            <p className="made-with">
              Made with <FaHeart className="heart-icon" /> for academic excellence
            </p>
          </div>
        </motion.div>
      </div>
    </footer>
  );
};

export default Footer;
