import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link, useLocation } from 'react-router-dom';
import { FaBars, FaTimes, FaGraduationCap } from 'react-icons/fa';
import '../styles/Header.css';

const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const location = useLocation();

  const navItems = [
    { id: '/', label: 'Home' },
    { id: '/about', label: 'About' },
    { id: '/research', label: 'Research' },
    { id: '/publications', label: 'Publications' },
    { id: '/teaching', label: 'Teaching' },
    { id: '/contact', label: 'Contact' }
  ];

  const resumeLink = {
    href: '/hymavathi.pdf',
    label: 'Resume',
    download: 'Hymavathi_Resume.pdf'
  };

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = () => {
    setIsMobileMenuOpen(false);
  };

  return (
    <motion.header
      className={`header ${isScrolled ? 'scrolled' : ''}`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="container">
        <div className="header-content">
          <Link to="/">
            <motion.div
              className="logo"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
              transition={{ type: "spring", stiffness: 400, damping: 25 }}
            >
              <img
                src="/images/logo-transparent.png"
                alt="Hymavathi - Motivate Educate Elevate"
                className="header-logo-image"
              />
            </motion.div>
          </Link>

          <nav className="nav-desktop">
            <ul className="nav-list">
              {navItems.map((item) => (
                <motion.li
                  key={item.id}
                  whileHover={{ y: -1 }}
                  transition={{ type: "spring", stiffness: 400, damping: 25 }}
                >
                  <Link
                    to={item.id}
                    className={`nav-link ${location.pathname === item.id ? 'active' : ''}`}
                    onClick={handleNavClick}
                  >
                    {item.label}
                    {location.pathname === item.id && (
                      <motion.div
                        className="nav-indicator"
                        layoutId="nav-indicator"
                        transition={{ type: "spring", stiffness: 400, damping: 30 }}
                      />
                    )}
                  </Link>
                </motion.li>
              ))}

              {/* Resume Download Link */}
              <motion.li
                whileHover={{ y: -1 }}
                transition={{ type: "spring", stiffness: 400, damping: 25 }}
              >
                <a
                  href={resumeLink.href}
                  className="nav-link resume-link"
                  download={resumeLink.download}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {resumeLink.label}
                </a>
              </motion.li>
            </ul>
          </nav>

          <motion.button
            className="mobile-menu-toggle"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            aria-label="Toggle mobile menu"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            transition={{ type: "spring", stiffness: 400, damping: 25 }}
          >
            <motion.div
              animate={{ rotate: isMobileMenuOpen ? 180 : 0 }}
              transition={{ duration: 0.3 }}
            >
              {isMobileMenuOpen ? <FaTimes /> : <FaBars />}
            </motion.div>
          </motion.button>
        </div>
      </div>

      {/* Professional Mobile Menu */}
      <AnimatePresence>
        {isMobileMenuOpen && (
          <motion.div
            className="mobile-menu"
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
          >
        <nav className="mobile-nav">
          <ul className="mobile-nav-list">
            {navItems.map((item, index) => (
              <motion.li
                key={item.id}
                initial={{ opacity: 0, x: -30 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{
                  delay: index * 0.1,
                  type: "spring",
                  stiffness: 300,
                  damping: 25
                }}
              >
                <Link
                  to={item.id}
                  className={`mobile-nav-link ${location.pathname === item.id ? 'active' : ''}`}
                  onClick={handleNavClick}
                >
                  {item.label}
                </Link>
              </motion.li>
            ))}

            {/* Mobile Resume Download Link */}
            <motion.li
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                delay: navItems.length * 0.1,
                type: "spring",
                stiffness: 300,
                damping: 25
              }}
            >
              <a
                href={resumeLink.href}
                className="mobile-nav-link resume-link"
                download={resumeLink.download}
                target="_blank"
                rel="noopener noreferrer"
                onClick={handleNavClick}
              >
                {resumeLink.label}
              </a>
            </motion.li>
          </ul>
        </nav>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.header>
  );
};

export default Header;
