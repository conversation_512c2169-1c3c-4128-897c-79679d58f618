import React, { useState } from 'react';

const ProfileImage = ({ 
  src = "/images/hymavathi.jpg", 
  alt = "Ms. Hymavathi Thottathyl", 
  className = "profile-photo",
  fallbackIcon = "👩‍🏫",
  style = {}
}) => {
  const [imageError, setImageError] = useState(false);

  const handleImageError = () => {
    setImageError(true);
  };

  if (imageError) {
    return (
      <div 
        className="profile-fallback"
        style={{
          width: '100%',
          height: '100%',
          borderRadius: '50%',
          background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          color: 'white',
          fontSize: '3rem',
          ...style
        }}
      >
        {fallbackIcon}
      </div>
    );
  }

  return (
    <img 
      src={src}
      alt={alt}
      className={className}
      style={style}
      onError={handleImageError}
      loading="lazy"
    />
  );
};

export default ProfileImage;
