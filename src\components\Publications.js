import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>aBook, FaExternalLinkAlt, FaQuoteLeft, FaFilter, FaSearch } from 'react-icons/fa';
import '../styles/Publications.css';

const Publications = () => {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  const categories = [
    { id: 'all', label: 'All Publications' },
    { id: 'journal', label: 'Journal Articles' },
    { id: 'conference', label: 'Conference Papers' },
    { id: 'book', label: 'Books & Chapters' }
  ];

  const publications = [
    {
      id: 1,
      title: "Prediction of the most influenced gene in the development of breast cancer using the DE-LSTM model",
      authors: "<PERSON><PERSON><PERSON><PERSON>, K Karteeka Pavan",
      journal: "Ingénierie des Systèmes d'Information",
      year: 2025,
      category: "journal",
      citations: 0,
      impact: "High Impact",
      abstract: "This paper presents a novel DE-LSTM model for predicting the most influenced genes in breast cancer development, contributing to early detection and treatment strategies.",
      doi: "https://doi.org/10.18280/isi.300124"
    },
    {
      id: 2,
      title: "Differential Evolution model for identification of most influenced gene in Breast cancer data",
      authors: "Hymavathi Thottathyl, K Karteeka Pavan",
      journal: "Ingénierie des Systèmes d'Information",
      year: 2022,
      category: "journal",
      citations: 5,
      impact: "High Impact",
      abstract: "This research presents a Differential Evolution model for identifying the most influenced genes in breast cancer data, providing insights for targeted therapy approaches.",
      doi: "https://doi.org/10.18280/isi.270316"
    },
    {
      id: 3,
      title: "Microarray Breast Cancer Data Clustering Using Map Reduce Based K-Means Algorithm",
      authors: "Hymavathi Thottathyl, K Karteeka Pavan",
      journal: "Revue d'Intelligence Artificielle",
      year: 2020,
      category: "journal",
      citations: 12,
      impact: "High Impact",
      abstract: "This paper presents a novel approach for clustering microarray breast cancer data using Map Reduce based K-Means algorithm, improving computational efficiency and accuracy.",
      doi: "https://doi.org/10.18280/ria.340610"
    },
    {
      id: 2,
      title: "Ethical Frameworks for AI Decision Making",
      authors: "Dr. Academic, A. Wilson, R. Brown",
      journal: "Proceedings of ICML 2023",
      year: 2023,
      category: "conference",
      citations: 89,
      impact: "Medium Impact",
      abstract: "We propose a comprehensive ethical framework for AI systems that ensures fairness, transparency, and accountability in automated decision-making processes.",
      doi: "10.48550/arXiv.2023.12345"
    },
    {
      id: 3,
      title: "Machine Learning in Healthcare: A Comprehensive Guide",
      authors: "Dr. Academic, K. Davis",
      journal: "Academic Press",
      year: 2022,
      category: "book",
      citations: 234,
      impact: "High Impact",
      abstract: "A comprehensive textbook covering the application of machine learning techniques in healthcare, from basic concepts to advanced implementations.",
      doi: "978-0-12-345678-9"
    },
    {
      id: 4,
      title: "Reinforcement Learning for Robotics Applications",
      authors: "Dr. Academic, L. Martinez, P. Anderson",
      journal: "IEEE Transactions on Robotics",
      year: 2022,
      category: "journal",
      citations: 178,
      impact: "High Impact",
      abstract: "This work demonstrates the application of reinforcement learning algorithms in complex robotics scenarios, improving task completion rates by 40%.",
      doi: "10.1109/TRO.2022.1234567"
    },
    {
      id: 5,
      title: "Natural Language Processing for Scientific Literature Analysis",
      authors: "Dr. Academic, S. Thompson, C. Lee",
      journal: "Proceedings of ACL 2022",
      year: 2022,
      category: "conference",
      citations: 92,
      impact: "Medium Impact",
      abstract: "We present a novel NLP framework for analyzing scientific literature, enabling automated extraction of research trends and knowledge gaps.",
      doi: "10.18653/v1/2022.acl-long.123"
    }
  ];

  const filteredPublications = publications.filter(pub => {
    const matchesCategory = selectedCategory === 'all' || pub.category === selectedCategory;
    const matchesSearch = pub.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pub.authors.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         pub.journal.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesCategory && matchesSearch;
  });

  const totalCitations = publications.reduce((sum, pub) => sum + pub.citations, 0);

  return (
    <section id="publications" className="section publications">
      <div className="container">
        <motion.h2
          className="section-title"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          Publications & Research Output
        </motion.h2>

        {/* Publication Stats */}
        <motion.div
          className="publication-stats"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="stat-item">
            <div className="stat-number">{publications.length}</div>
            <div className="stat-label">Publications</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">{totalCitations}</div>
            <div className="stat-label">Total Citations</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">42</div>
            <div className="stat-label">H-Index</div>
          </div>
          <div className="stat-item">
            <div className="stat-number">15</div>
            <div className="stat-label">Years Active</div>
          </div>
        </motion.div>

        {/* Filters and Search */}
        <motion.div
          className="publication-controls"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <div className="search-box">
            <FaSearch className="search-icon" />
            <input
              type="text"
              placeholder="Search publications..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="search-input"
            />
          </div>
          
          <div className="category-filters">
            <FaFilter className="filter-icon" />
            {categories.map(category => (
              <button
                key={category.id}
                className={`filter-btn ${selectedCategory === category.id ? 'active' : ''}`}
                onClick={() => setSelectedCategory(category.id)}
              >
                {category.label}
              </button>
            ))}
          </div>
        </motion.div>

        {/* Publications List */}
        <div className="publications-list">
          {filteredPublications.map((publication, index) => (
            <motion.div
              key={publication.id}
              className="publication-card"
              initial={{ opacity: 0, y: 30 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
              whileHover={{ y: -5 }}
            >
              <div className="publication-header">
                <div className="publication-icon">
                  <FaBook />
                </div>
                <div className="publication-meta">
                  <span className={`impact-badge ${publication.impact.toLowerCase().replace(' ', '-')}`}>
                    {publication.impact}
                  </span>
                  <span className="publication-year">{publication.year}</span>
                </div>
              </div>

              <h3 className="publication-title">{publication.title}</h3>
              <p className="publication-authors">{publication.authors}</p>
              <p className="publication-journal">{publication.journal}</p>

              <div className="publication-abstract">
                <FaQuoteLeft className="quote-icon" />
                <p>{publication.abstract}</p>
              </div>

              <div className="publication-footer">
                <div className="publication-stats">
                  <span className="citations">{publication.citations} citations</span>
                  <span className="doi">DOI: {publication.doi}</span>
                </div>
                <button className="btn btn-outline publication-link">
                  <FaExternalLinkAlt />
                  View Paper
                </button>
              </div>
            </motion.div>
          ))}
        </div>

        {filteredPublications.length === 0 && (
          <motion.div
            className="no-results"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          >
            <p>No publications found matching your criteria.</p>
          </motion.div>
        )}
      </div>
    </section>
  );
};

export default Publications;
