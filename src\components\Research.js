import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaRobot, FaBrain, FaEye, FaLanguage, FaChartLine, FaExternalLinkAlt } from 'react-icons/fa';
import '../styles/Research.css';

const Research = () => {
  const [activeProject, setActiveProject] = useState(0);

  const researchAreas = [
    {
      icon: <FaRobot />,
      title: "Artificial Intelligence",
      description: "Developing intelligent systems that can learn, reason, and adapt to complex environments."
    },
    {
      icon: <FaBrain />,
      title: "Machine Learning",
      description: "Creating algorithms that enable computers to learn from data without explicit programming."
    },
    {
      icon: <FaEye />,
      title: "Computer Vision",
      description: "Teaching machines to interpret and understand visual information from the world."
    },
    {
      icon: <FaLanguage />,
      title: "Natural Language Processing",
      description: "Enabling computers to understand, interpret, and generate human language."
    }
  ];

  const projects = [
    {
      title: "Autonomous Learning Systems",
      description: "Developing self-improving AI systems that can adapt to new environments and tasks without human intervention.",
      status: "Ongoing",
      funding: "$2.5M NSF Grant",
      collaborators: ["MIT", "Stanford", "Google Research"],
      publications: 8,
      image: "🤖"
    },
    {
      title: "Medical Image Analysis",
      description: "Using deep learning to improve diagnostic accuracy in medical imaging, particularly in early cancer detection.",
      status: "Completed",
      funding: "$1.8M NIH Grant",
      collaborators: ["Mayo Clinic", "Johns Hopkins"],
      publications: 12,
      image: "🏥"
    },
    {
      title: "Ethical AI Framework",
      description: "Creating guidelines and tools for developing responsible AI systems that prioritize fairness and transparency.",
      status: "Ongoing",
      funding: "$900K Industry Partnership",
      collaborators: ["IBM", "Microsoft", "Ethics Institute"],
      publications: 6,
      image: "⚖️"
    }
  ];

  return (
    <section id="research" className="section research">
      <div className="container">
        <motion.h2
          className="section-title"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          Research & Innovation
        </motion.h2>

        {/* Research Areas */}
        <div className="research-areas">
          <motion.h3
            className="subsection-title"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Research Areas
          </motion.h3>
          
          <div className="areas-grid">
            {researchAreas.map((area, index) => (
              <motion.div
                key={area.title}
                className="area-card"
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                whileHover={{ y: -5 }}
              >
                <div className="area-icon">{area.icon}</div>
                <h4>{area.title}</h4>
                <p>{area.description}</p>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Current Projects */}
        <div className="current-projects">
          <motion.h3
            className="subsection-title"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
          >
            Current Projects
          </motion.h3>

          <div className="projects-container">
            <div className="project-tabs">
              {projects.map((project, index) => (
                <button
                  key={project.title}
                  className={`project-tab ${activeProject === index ? 'active' : ''}`}
                  onClick={() => setActiveProject(index)}
                >
                  <span className="tab-icon">{project.image}</span>
                  <span className="tab-title">{project.title}</span>
                </button>
              ))}
            </div>

            <motion.div
              className="project-content"
              key={activeProject}
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.5 }}
            >
              <div className="project-header">
                <div className="project-icon">{projects[activeProject].image}</div>
                <div className="project-info">
                  <h4>{projects[activeProject].title}</h4>
                  <span className={`project-status ${projects[activeProject].status.toLowerCase()}`}>
                    {projects[activeProject].status}
                  </span>
                </div>
              </div>

              <p className="project-description">
                {projects[activeProject].description}
              </p>

              <div className="project-details">
                <div className="detail-item">
                  <strong>Funding:</strong> {projects[activeProject].funding}
                </div>
                <div className="detail-item">
                  <strong>Collaborators:</strong> {projects[activeProject].collaborators.join(', ')}
                </div>
                <div className="detail-item">
                  <strong>Publications:</strong> {projects[activeProject].publications} papers
                </div>
              </div>

              <button className="btn btn-outline project-link">
                <FaExternalLinkAlt />
                View Project Details
              </button>
            </motion.div>
          </div>
        </div>

        {/* Research Impact */}
        <motion.div
          className="research-impact"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          viewport={{ once: true }}
        >
          <div className="impact-card">
            <FaChartLine className="impact-icon" />
            <div className="impact-content">
              <h4>Research Impact</h4>
              <p>
                My research has been cited over 5,000 times and has directly influenced 
                industry standards in AI ethics and medical imaging applications.
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
};

export default Research;
