import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { FaGraduationCap, FaChalkboardTeacher, FaStar, FaUsers, FaBook, FaLaptopCode } from 'react-icons/fa';
import '../styles/Teaching.css';

const Teaching = () => {
  const [activeTab, setActiveTab] = useState('courses');

  const courses = [
    {
      code: "CS 401",
      title: "Introduction to Artificial Intelligence",
      level: "Undergraduate",
      students: 120,
      rating: 4.8,
      description: "Comprehensive introduction to AI concepts, algorithms, and applications.",
      topics: ["Search Algorithms", "Machine Learning Basics", "Neural Networks", "Expert Systems"]
    },
    {
      code: "CS 601",
      title: "Advanced Machine Learning",
      level: "Graduate",
      students: 45,
      rating: 4.9,
      description: "Deep dive into advanced ML techniques and research methodologies.",
      topics: ["Deep Learning", "Reinforcement Learning", "Bayesian Methods", "Ensemble Methods"]
    },
    {
      code: "CS 501",
      title: "Computer Vision",
      level: "Graduate",
      students: 35,
      rating: 4.7,
      description: "Image processing, pattern recognition, and computer vision applications.",
      topics: ["Image Processing", "Feature Detection", "Object Recognition", "Deep Vision"]
    },
    {
      code: "CS 301",
      title: "Data Structures and Algorithms",
      level: "Undergraduate",
      students: 200,
      rating: 4.6,
      description: "Fundamental data structures and algorithmic problem-solving techniques.",
      topics: ["Arrays & Lists", "Trees & Graphs", "Sorting & Searching", "Dynamic Programming"]
    }
  ];

  const achievements = [
    {
      icon: <FaGraduationCap />,
      title: "Excellence in Teaching Award",
      year: "2023",
      description: "University-wide recognition for outstanding teaching performance"
    },
    {
      icon: <FaStar />,
      title: "Best Professor Award",
      year: "2022",
      description: "Student-voted award for exceptional mentorship and guidance"
    },
    {
      icon: <FaBook />,
      title: "Curriculum Innovation Award",
      year: "2021",
      description: "Recognition for developing innovative AI curriculum"
    }
  ];

  const testimonials = [
    {
      name: "Sarah Johnson",
      course: "CS 601 - Advanced Machine Learning",
      text: "Dr. Academic's teaching style is exceptional. Complex concepts become clear through practical examples and hands-on projects.",
      rating: 5
    },
    {
      name: "Michael Chen",
      course: "CS 401 - Introduction to AI",
      text: "The best professor I've had. Always available for questions and genuinely cares about student success.",
      rating: 5
    },
    {
      name: "Emily Rodriguez",
      course: "CS 501 - Computer Vision",
      text: "Challenging course but incredibly rewarding. The projects were directly applicable to real-world problems.",
      rating: 5
    }
  ];

  const renderStars = (rating) => {
    return Array.from({ length: 5 }, (_, i) => (
      <FaStar
        key={i}
        className={`star ${i < rating ? 'filled' : ''}`}
      />
    ));
  };

  return (
    <section id="teaching" className="section teaching">
      <div className="container">
        <motion.h2
          className="section-title"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
        >
          Teaching & Mentorship
        </motion.h2>

        {/* Teaching Philosophy */}
        <motion.div
          className="teaching-philosophy"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          <div className="philosophy-content">
            <FaChalkboardTeacher className="philosophy-icon" />
            <div className="philosophy-text">
              <h3>Teaching Philosophy</h3>
              <p>
                I believe in creating an inclusive, engaging learning environment where students 
                are encouraged to think critically and explore innovative solutions. My approach 
                combines theoretical foundations with practical applications, ensuring students 
                gain both deep understanding and real-world skills.
              </p>
            </div>
          </div>
        </motion.div>

        {/* Tab Navigation */}
        <motion.div
          className="teaching-tabs"
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: 0.4 }}
          viewport={{ once: true }}
        >
          <button
            className={`tab-btn ${activeTab === 'courses' ? 'active' : ''}`}
            onClick={() => setActiveTab('courses')}
          >
            <FaBook />
            Courses
          </button>
          <button
            className={`tab-btn ${activeTab === 'achievements' ? 'active' : ''}`}
            onClick={() => setActiveTab('achievements')}
          >
            <FaStar />
            Achievements
          </button>
          <button
            className={`tab-btn ${activeTab === 'testimonials' ? 'active' : ''}`}
            onClick={() => setActiveTab('testimonials')}
          >
            <FaUsers />
            Testimonials
          </button>
        </motion.div>

        {/* Tab Content */}
        <div className="tab-content">
          {activeTab === 'courses' && (
            <motion.div
              className="courses-grid"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {courses.map((course, index) => (
                <motion.div
                  key={course.code}
                  className="course-card"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                >
                  <div className="course-header">
                    <div className="course-code">{course.code}</div>
                    <div className="course-level">{course.level}</div>
                  </div>
                  
                  <h4 className="course-title">{course.title}</h4>
                  <p className="course-description">{course.description}</p>
                  
                  <div className="course-topics">
                    {course.topics.map(topic => (
                      <span key={topic} className="topic-tag">{topic}</span>
                    ))}
                  </div>
                  
                  <div className="course-stats">
                    <div className="stat">
                      <FaUsers />
                      <span>{course.students} students</span>
                    </div>
                    <div className="stat">
                      <div className="rating">
                        {renderStars(Math.floor(course.rating))}
                        <span className="rating-number">{course.rating}</span>
                      </div>
                    </div>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}

          {activeTab === 'achievements' && (
            <motion.div
              className="achievements-grid"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {achievements.map((achievement, index) => (
                <motion.div
                  key={achievement.title}
                  className="achievement-card"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ scale: 1.02 }}
                >
                  <div className="achievement-icon">{achievement.icon}</div>
                  <div className="achievement-content">
                    <h4>{achievement.title}</h4>
                    <div className="achievement-year">{achievement.year}</div>
                    <p>{achievement.description}</p>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}

          {activeTab === 'testimonials' && (
            <motion.div
              className="testimonials-grid"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              {testimonials.map((testimonial, index) => (
                <motion.div
                  key={testimonial.name}
                  className="testimonial-card"
                  initial={{ opacity: 0, y: 30 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  whileHover={{ y: -5 }}
                >
                  <div className="testimonial-rating">
                    {renderStars(testimonial.rating)}
                  </div>
                  <p className="testimonial-text">"{testimonial.text}"</p>
                  <div className="testimonial-author">
                    <strong>{testimonial.name}</strong>
                    <span className="testimonial-course">{testimonial.course}</span>
                  </div>
                </motion.div>
              ))}
            </motion.div>
          )}
        </div>
      </div>
    </section>
  );
};

export default Teaching;
