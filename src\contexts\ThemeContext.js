import React, { createContext, useContext, useState, useEffect } from 'react';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export const ThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check localStorage for saved theme preference
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      return savedTheme === 'dark';
    }
    // Check system preference
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  const [isTransitioning, setIsTransitioning] = useState(false);

  useEffect(() => {
    // Save theme preference to localStorage
    localStorage.setItem('theme', isDarkMode ? 'dark' : 'light');

    // Apply theme to document root immediately for faster switching
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');

    // Add transition class to body for smoother theme switching
    document.body.classList.add('theme-transitioning');

    // Update CSS custom properties with enhanced color contrast
    const root = document.documentElement;
    if (isDarkMode) {
      // Dark mode colors with enhanced contrast
      root.style.setProperty('--primary-color', '#64b5f6');
      root.style.setProperty('--secondary-color', '#81c784');
      root.style.setProperty('--accent-color', '#ffb74d');
      root.style.setProperty('--background-primary', '#0f0f0f');
      root.style.setProperty('--background-secondary', '#1a1a1a');
      root.style.setProperty('--background-light', '#2a2a2a');
      root.style.setProperty('--background-dark', '#050505');
      root.style.setProperty('--text-primary', '#ffffff');
      root.style.setProperty('--text-secondary', '#cccccc');
      root.style.setProperty('--text-muted', '#999999');
      root.style.setProperty('--text-light', '#ffffff');
      root.style.setProperty('--border-color', '#333333');
      root.style.setProperty('--shadow-light', '0 2px 10px rgba(0, 0, 0, 0.4)');
      root.style.setProperty('--shadow-medium', '0 4px 20px rgba(0, 0, 0, 0.5)');
      root.style.setProperty('--shadow-heavy', '0 8px 30px rgba(0, 0, 0, 0.6)');
      root.style.setProperty('--gradient-primary', 'linear-gradient(135deg, #1a237e 0%, #3949ab 100%)');
      root.style.setProperty('--gradient-secondary', 'linear-gradient(135deg, #2e7d32 0%, #66bb6a 100%)');

      // Set body background for smooth transition
      document.body.style.backgroundColor = '#0f0f0f';
      document.body.style.color = '#ffffff';
    } else {
      // Light mode colors with enhanced contrast
      root.style.setProperty('--primary-color', '#1976d2');
      root.style.setProperty('--secondary-color', '#388e3c');
      root.style.setProperty('--accent-color', '#f57c00');
      root.style.setProperty('--background-primary', '#ffffff');
      root.style.setProperty('--background-secondary', '#f8f9fa');
      root.style.setProperty('--background-light', '#f0f2f5');
      root.style.setProperty('--background-dark', '#e0e0e0');
      root.style.setProperty('--text-primary', '#212121');
      root.style.setProperty('--text-secondary', '#444444');
      root.style.setProperty('--text-muted', '#666666');
      root.style.setProperty('--text-light', '#212121');
      root.style.setProperty('--border-color', '#d0d0d0');
      root.style.setProperty('--shadow-light', '0 2px 10px rgba(0, 0, 0, 0.08)');
      root.style.setProperty('--shadow-medium', '0 4px 20px rgba(0, 0, 0, 0.12)');
      root.style.setProperty('--shadow-heavy', '0 8px 30px rgba(0, 0, 0, 0.16)');
      root.style.setProperty('--gradient-primary', 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)');
      root.style.setProperty('--gradient-secondary', 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)');

      // Set body background for smooth transition
      document.body.style.backgroundColor = '#ffffff';
      document.body.style.color = '#212121';
    }

    // Remove transition class after animation completes - reduced time for faster switching
    setTimeout(() => {
      document.body.classList.remove('theme-transitioning');
    }, 400);
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsTransitioning(true);
    setIsDarkMode(prev => !prev);

    // Reset transition state after animation - reduced time for faster switching
    setTimeout(() => {
      setIsTransitioning(false);
    }, 400);
  };

  const value = {
    isDarkMode,
    toggleTheme,
    isTransitioning,
    theme: isDarkMode ? 'dark' : 'light'
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};
