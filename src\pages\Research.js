import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';
import { FaRobot, FaBrain, FaEye, FaLanguage, FaChartLine, FaExternalLinkAlt, FaArrowLeft, FaLightbulb, FaCertificate, FaUsers, FaDollarSign } from 'react-icons/fa';
import '../styles/Research.css';
import '../styles/PageLayout.css';

const Research = () => {
  const [activeProject, setActiveProject] = useState(0);

  const researchAreas = [
    {
      icon: <FaRobot />,
      title: "Bio-Informatics",
      description: "Specializing in microarray gene clustering and breast cancer gene prediction using advanced computational methods."
    },
    {
      icon: <FaBrain />,
      title: "Machine Learning",
      description: "Developing DE-LSTM models and differential evolution algorithms for healthcare applications and gene identification."
    },
    {
      icon: <FaEye />,
      title: "AI-Driven Solutions",
      description: "Creating AI-driven personal assistants with multiplatform integration and intelligent automation systems."
    },
    {
      icon: <FaLanguage />,
      title: "Data Mining & Clustering",
      description: "Implementing Map Reduce based K-Means algorithms for efficient processing of large-scale biological datasets."
    }
  ];

  const patents = [
    {
      id: 1,
      patentNo: "202341057851 A",
      title: "An Artificial Intelligence (AI) Driven Personal Assistant with Multiplatform Integration",
      inventors: "Dr. K. Karteeka Pavan, Dr.Ch.Suneetha, Dr.M.Sridhar, Ms.Hymavathi T",
      filedDate: "29th AUG -2023",
      publishedDate: "8th SEP-2023",
      status: "Published",
      description: "This patent presents an innovative AI-driven personal assistant system with multiplatform integration capabilities, enabling seamless user interaction across various devices and platforms."
    }
  ];

  const researchProjects = [
    {
      title: "Novel Algorithms for Microarray Gene Clustering in Association with Breast Cancer",
      description: "Internal Research Project focusing on developing novel algorithms for microarray gene clustering specifically targeting breast cancer research.",
      status: "Completed",
      funding: "₹30,000 Research Grant",
      fileNo: "R&J/IRP/2019-20/605",
      institution: "R.V.R & J.C College of Engineering",
      role: "Principal Investigator",
      coInvestigator: "Dr. K.Karteeka Pavan (Professor & HoD)",
      outcome: "Successfully completed and submitted to RVRJCCE"
    }
  ];

  const projects = [
    {
      title: "DELSTM Model for Breast Cancer Gene Prediction",
      description: "Developing a novel DELSTM model to predict the most influenced genes in breast cancer development for early detection and treatment.",
      status: "Published",
      funding: "University Research Grant",
      collaborators: ["Kanadam Karteeka Pavan", "Acharya Nagarjuna University"],
      publications: 2,
      image: "🧬"
    },
    {
      title: "Cyber Attack Detection in Network Traffic",
      description: "Enhancing network security through adaptive regression techniques for improved cyber attack detection capabilities.",
      status: "Published",
      funding: "Collaborative Research",
      collaborators: ["Dr. Talluri Sunil Kumar", "Multiple Institutions"],
      publications: 1,
      image: "🔒"
    },
    {
      title: "AI-Driven Personal Assistant",
      description: "Developing an AI-driven personal assistant with multi-platform integration capabilities for enhanced user experience.",
      status: "Patent Filed",
      funding: "Innovation Grant",
      collaborators: ["Research Team"],
      publications: 1,
      image: "🤖"
    }
  ];

  return (
    <div className="page-container">
      {/* Page Header */}
      <motion.div
        className="page-header"
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <div className="container">
          <Link to="/" className="back-link">
            <FaArrowLeft />
            Back to Home
          </Link>
          <h1 className="page-title">Research & Innovation</h1>
          <p className="page-subtitle">Explore my research areas, current projects, and innovations</p>
        </div>
      </motion.div>

      <section className="section research">
        <div className="container">
          {/* Research Areas */}
          <div className="research-areas">
            <motion.h3
              className="subsection-title"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              Research Areas
            </motion.h3>
            
            <div className="areas-grid">
              {researchAreas.map((area, index) => (
                <motion.div
                  key={area.title}
                  className="area-card"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="area-icon">{area.icon}</div>
                  <h4>{area.title}</h4>
                  <p>{area.description}</p>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Patents Section */}
          <div className="patents-section">
            <motion.h3
              className="subsection-title"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              Patents & Intellectual Property
            </motion.h3>

            <div className="patents-grid">
              {patents.map((patent, index) => (
                <motion.div
                  key={patent.id}
                  className="patent-card"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.9 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="patent-header">
                    <div className="patent-icon">
                      <FaLightbulb />
                    </div>
                    <div className="patent-status">
                      <span className={`status-badge ${patent.status.toLowerCase()}`}>
                        {patent.status}
                      </span>
                    </div>
                  </div>

                  <h4 className="patent-title">{patent.title}</h4>
                  <p className="patent-number">Patent No: {patent.patentNo}</p>
                  <p className="patent-description">{patent.description}</p>

                  <div className="patent-details">
                    <div className="detail-item">
                      <strong>Inventors:</strong> {patent.inventors}
                    </div>
                    <div className="detail-item">
                      <strong>Filed:</strong> {patent.filedDate}
                    </div>
                    <div className="detail-item">
                      <strong>Published:</strong> {patent.publishedDate}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Research Projects */}
          <div className="research-projects-section">
            <motion.h3
              className="subsection-title"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.0 }}
            >
              Completed Research Projects
            </motion.h3>

            <div className="projects-grid">
              {researchProjects.map((project, index) => (
                <motion.div
                  key={index}
                  className="research-project-card"
                  initial={{ opacity: 0, y: 30 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 1.1 + index * 0.1 }}
                  whileHover={{ y: -5 }}
                >
                  <div className="project-header">
                    <div className="project-icon">
                      <FaCertificate />
                    </div>
                    <div className="project-status">
                      <span className={`status-badge ${project.status.toLowerCase()}`}>
                        {project.status}
                      </span>
                    </div>
                  </div>

                  <h4 className="project-title">{project.title}</h4>
                  <p className="project-description">{project.description}</p>

                  <div className="project-details">
                    <div className="detail-item">
                      <strong>File No:</strong> {project.fileNo}
                    </div>
                    <div className="detail-item">
                      <strong>Funding:</strong> {project.funding}
                    </div>
                    <div className="detail-item">
                      <strong>Role:</strong> {project.role}
                    </div>
                    <div className="detail-item">
                      <strong>Co-Investigator:</strong> {project.coInvestigator}
                    </div>
                    <div className="detail-item">
                      <strong>Outcome:</strong> {project.outcome}
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Current Projects */}
          <div className="current-projects">
            <motion.h3
              className="subsection-title"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.8 }}
            >
              Current Projects
            </motion.h3>

            <motion.div
              className="projects-container"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 1.0 }}
            >
              <div className="project-tabs">
                {projects.map((project, index) => (
                  <button
                    key={project.title}
                    className={`project-tab ${activeProject === index ? 'active' : ''}`}
                    onClick={() => setActiveProject(index)}
                  >
                    <span className="tab-icon">{project.image}</span>
                    <span className="tab-title">{project.title}</span>
                  </button>
                ))}
              </div>

              <motion.div
                className="project-content"
                key={activeProject}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.5 }}
              >
                <div className="project-header">
                  <div className="project-icon">{projects[activeProject].image}</div>
                  <div className="project-info">
                    <h4>{projects[activeProject].title}</h4>
                    <span className={`project-status ${projects[activeProject].status.toLowerCase()}`}>
                      {projects[activeProject].status}
                    </span>
                  </div>
                </div>

                <p className="project-description">
                  {projects[activeProject].description}
                </p>

                <div className="project-details">
                  <div className="detail-item">
                    <strong>Funding:</strong> {projects[activeProject].funding}
                  </div>
                  <div className="detail-item">
                    <strong>Collaborators:</strong> {projects[activeProject].collaborators.join(', ')}
                  </div>
                  <div className="detail-item">
                    <strong>Publications:</strong> {projects[activeProject].publications} papers
                  </div>
                </div>

                <button className="btn btn-outline project-link">
                  <FaExternalLinkAlt />
                  View Project Details
                </button>
              </motion.div>
            </motion.div>
          </div>

          {/* Research Impact */}
          <motion.div
            className="research-impact"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 1.2 }}
          >
            <div className="impact-card">
              <FaChartLine className="impact-icon" />
              <div className="impact-content">
                <h4>Research Impact</h4>
                <p>
                  My research focuses on practical applications of AI in healthcare and cybersecurity,
                  contributing to early cancer detection methods and enhanced network security protocols.
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default Research;
