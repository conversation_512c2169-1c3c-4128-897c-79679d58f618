/* Professional About Page */
.about {
  background: var(--background-primary);
  padding: var(--space-3xl) 0;
  position: relative;
}

[data-theme="dark"] .about {
  background: var(--background-dark);
}

.about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
}

/* Professional About Content Layout */
.about-content {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: var(--space-3xl);
  align-items: start;
  margin-bottom: var(--space-3xl);
}

.about-text {
  max-width: 700px;
}

.about-intro h3 {
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 700;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.about-intro p {
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: var(--space-lg);
  font-size: 1.125rem;
  font-weight: 400;
}

/* Professional Expertise Section */
.expertise-section {
  margin-top: var(--space-2xl);
  padding: var(--space-2xl);
  background: var(--background-secondary);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-light);
}

[data-theme="dark"] .expertise-section {
  background: rgba(55, 65, 81, 0.3);
  border-color: rgba(75, 85, 99, 0.3);
}

.expertise-section h4 {
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  font-size: clamp(1.25rem, 2.5vw, 1.5rem);
  font-weight: 600;
  text-align: center;
  position: relative;
}

.expertise-section h4::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: var(--radius-full);
}

.expertise-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: var(--space-md);
}

.expertise-item {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--text-inverse);
  padding: var(--space-md) var(--space-lg);
  border-radius: var(--radius-xl);
  text-align: center;
  font-weight: 600;
  font-size: 0.95rem;
  transition: all var(--transition-medium);
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.expertise-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
  z-index: 1;
}

.expertise-item:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-lg);
  filter: brightness(1.1);
}

/* Professional About Image */
.about-image {
  display: flex;
  justify-content: center;
  align-items: flex-start;
  position: relative;
}

.about-image .image-placeholder {
  width: 400px;
  height: 500px;
  border-radius: var(--radius-2xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  box-shadow: var(--shadow-2xl);
  position: relative;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.1);
}

.about-image .image-placeholder::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.15), transparent);
  transform: rotate(45deg);
  animation: shimmer 4s infinite;
}

.about-image .placeholder-content {
  text-align: center;
  z-index: 2;
  position: relative;
}

.about-image .placeholder-icon {
  font-size: 5rem;
  margin-bottom: var(--space-lg);
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.2));
}

.about-image .placeholder-content p {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
  letter-spacing: -0.01em;
}

/* Professional Stats Section */
.stats-section {
  margin-top: var(--space-3xl);
  padding: var(--space-2xl) 0;
  background: var(--background-secondary);
  border-radius: var(--radius-2xl);
  border: 1px solid var(--border-light);
  position: relative;
}

[data-theme="dark"] .stats-section {
  background: rgba(17, 24, 39, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--space-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.stat-card {
  background: var(--background-card);
  padding: var(--space-2xl);
  border-radius: var(--radius-xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.stat-icon {
  font-size: 3rem;
  color: var(--secondary-color);
  margin-bottom: var(--space-lg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.stat-number {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--space-sm);
  font-family: 'Playfair Display', serif;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 1.1rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 0.95rem;
}

[data-theme="dark"] .stat-card {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .stat-card:hover {
  background: rgba(55, 65, 81, 0.8);
  border-color: var(--primary-light);
}

/* Professional Resume Download Section */
.resume-download-section {
  margin-top: var(--space-2xl);
  text-align: center;
}

.resume-download-section .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-lg) var(--space-2xl);
  font-size: 1.125rem;
  font-weight: 600;
  border-radius: var(--radius-xl);
  transition: all var(--transition-medium);
  text-decoration: none;
  box-shadow: var(--shadow-md);
}

.resume-download-section .btn:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
}

/* Professional Animations */
@keyframes shimmer {
  0% {
    transform: translateX(-100%) translateY(-100%) rotate(45deg);
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    transform: translateX(100%) translateY(100%) rotate(45deg);
    opacity: 0;
  }
}



/* Professional Responsive Design */
@media (max-width: 1200px) {
  .about-content {
    gap: var(--space-2xl);
    grid-template-columns: 1fr 0.9fr;
  }

  .profile-image-about,
  .about-image .image-placeholder {
    width: 350px;
    height: 450px;
  }
}

@media (max-width: 1024px) {
  .about-content {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
    text-align: center;
  }

  .about-text {
    max-width: 800px;
    margin: 0 auto;
  }

  .profile-image-about,
  .about-image .image-placeholder {
    width: 320px;
    height: 400px;
  }

  .expertise-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--space-sm);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
  }
}

@media (max-width: 768px) {
  .about {
    padding: var(--space-2xl) 0;
  }

  .about-content {
    gap: var(--space-xl);
  }

  .expertise-section {
    padding: var(--space-xl);
  }

  .expertise-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: var(--space-sm);
  }

  .expertise-item {
    font-size: 0.9rem;
    padding: var(--space-sm) var(--space-md);
  }

  .profile-image-about,
  .about-image .image-placeholder {
    width: 300px;
    height: 380px;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }

  .stat-card {
    padding: var(--space-lg);
  }

  .stat-icon {
    font-size: 2.5rem;
  }
}

@media (max-width: 480px) {
  .about {
    padding: var(--space-xl) 0;
  }

  .expertise-section {
    padding: var(--space-lg);
  }

  .expertise-grid {
    grid-template-columns: 1fr 1fr;
    gap: var(--space-xs);
  }

  .expertise-item {
    font-size: 0.8rem;
    padding: var(--space-xs) var(--space-sm);
  }

  .profile-image-about,
  .about-image .image-placeholder {
    width: 280px;
    height: 350px;
  }

  .about-image .placeholder-icon {
    font-size: 4rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .stat-card {
    padding: var(--space-md);
  }

  .stat-icon {
    font-size: 2rem;
  }

  .resume-download-section .btn {
    padding: var(--space-md) var(--space-lg);
    font-size: 1rem;
  }
}

/* Professional Profile Image Styles */
.profile-image-about {
  width: 400px;
  height: 500px;
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  margin: 0 auto;
  background: var(--background-card);
  padding: 12px;
  border: 4px solid rgba(255, 255, 255, 0.8);
  transition: all var(--transition-medium);
  position: relative;
}

[data-theme="dark"] .profile-image-about {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.5);
}

.profile-image-about::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.1) 0%, rgba(16, 185, 129, 0.1) 100%);
  border-radius: inherit;
  z-index: 1;
  pointer-events: none;
}

.about-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-xl);
  transition: all var(--transition-medium);
  filter: brightness(1.05) contrast(1.1);
  position: relative;
  z-index: 2;
}

.about-photo:hover {
  transform: scale(1.02);
  filter: brightness(1.1) contrast(1.15);
}

.profile-image-about:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-2xl);
  border-color: var(--primary-color);
}
