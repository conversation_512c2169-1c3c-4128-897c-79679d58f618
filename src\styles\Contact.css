/* Professional Contact Page */
.contact {
  background: var(--background-primary);
  padding: var(--space-3xl) 0;
  position: relative;
}

[data-theme="dark"] .contact {
  background: var(--background-dark);
}

.contact::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
}

.contact-content {
  display: grid;
  grid-template-columns: 1.1fr 0.9fr;
  gap: var(--space-3xl);
  align-items: start;
}

/* Professional Contact Information */
.contact-info h3 {
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 700;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-description {
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: var(--space-2xl);
  font-size: 1.125rem;
  font-weight: 400;
}

.contact-details {
  margin-bottom: var(--space-2xl);
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-lg);
  margin-bottom: var(--space-lg);
  padding: var(--space-xl);
  background: var(--background-card);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.contact-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.contact-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
  border-color: var(--primary-color);
}

[data-theme="dark"] .contact-item {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .contact-item:hover {
  background: rgba(55, 65, 81, 0.8);
  border-color: var(--primary-light);
}

.contact-icon {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-size: 1.4rem;
  flex-shrink: 0;
  box-shadow: var(--shadow-md);
  position: relative;
}

.contact-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
  border-radius: inherit;
}

.contact-text h4 {
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
  font-size: 1.25rem;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.contact-text p {
  color: var(--text-secondary);
  margin: 0;
  white-space: pre-line;
  font-size: 1rem;
  line-height: 1.6;
}

.contact-link {
  color: var(--primary-color);
  text-decoration: none;
  transition: all var(--transition-fast);
  font-weight: 500;
}

.contact-link:hover {
  color: var(--secondary-color);
  text-decoration: underline;
}

/* Professional Office Hours */
.office-hours {
  background: var(--background-card);
  padding: var(--space-xl);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  margin-bottom: var(--space-xl);
  position: relative;
  overflow: hidden;
}

.office-hours::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

[data-theme="dark"] .office-hours {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  backdrop-filter: blur(10px);
}

.office-hours h4 {
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  font-size: 1.375rem;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.hours-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-md);
}

.hour-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-md) 0;
  border-bottom: 1px solid var(--border-light);
  transition: all var(--transition-fast);
}

.hour-item:last-child {
  border-bottom: none;
}

.hour-item:hover {
  padding-left: var(--space-sm);
}

.day {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 1rem;
}

.time {
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.95rem;
}

/* Professional Social Links */
.social-links {
  background: var(--background-card);
  padding: var(--space-xl);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.social-links::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

[data-theme="dark"] .social-links {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  backdrop-filter: blur(10px);
}

.social-links h4 {
  color: var(--text-primary);
  margin-bottom: var(--space-lg);
  font-size: 1.375rem;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.social-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--space-md);
}

.social-link {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  padding: var(--space-md);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  color: var(--text-primary);
  text-decoration: none;
  transition: all var(--transition-medium);
  font-weight: 600;
  font-size: 0.95rem;
  background: var(--background-primary);
}

.social-link:hover {
  border-color: var(--social-color);
  color: var(--social-color);
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--shadow-md);
  background: rgba(255, 255, 255, 1);
}

[data-theme="dark"] .social-link {
  background: rgba(31, 41, 55, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

[data-theme="dark"] .social-link:hover {
  background: rgba(55, 65, 81, 0.8);
}

.social-link span {
  font-size: 0.9rem;
}

/* Professional Contact Form */
.contact-form-container {
  background: var(--background-card);
  padding: var(--space-2xl);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.contact-form-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

[data-theme="dark"] .contact-form-container {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  backdrop-filter: blur(10px);
}

.contact-form h3 {
  color: var(--text-primary);
  margin-bottom: var(--space-xl);
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  font-weight: 700;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.form-group {
  margin-bottom: var(--space-xl);
}

.form-group label {
  display: block;
  margin-bottom: var(--space-sm);
  color: var(--text-primary);
  font-weight: 600;
  font-size: 1rem;
  letter-spacing: -0.01em;
  font-weight: 500;
}

.form-group input,
.form-group textarea {
  width: 100%;
  padding: var(--space-lg);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-lg);
  font-size: 1rem;
  font-family: inherit;
  font-weight: 500;
  background: var(--background-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  resize: vertical;
}

.form-group input:focus,
.form-group textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-group input::placeholder,
.form-group textarea::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

[data-theme="dark"] .form-group input,
[data-theme="dark"] .form-group textarea {
  background: rgba(31, 41, 55, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  color: var(--text-primary);
}

[data-theme="dark"] .form-group input:focus,
[data-theme="dark"] .form-group textarea:focus {
  border-color: var(--primary-light);
  box-shadow: 0 0 0 3px rgba(100, 181, 246, 0.1);
}

.submit-btn {
  width: 100%;
  padding: var(--space-lg) var(--space-xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--text-inverse);
  border: none;
  border-radius: var(--radius-xl);
  font-size: 1.125rem;
  font-weight: 700;
  cursor: pointer;
  transition: all var(--transition-medium);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.submit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
  z-index: 1;
}

.submit-btn:hover:not(:disabled) {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
  filter: brightness(1.1);
}

.submit-btn:active:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg);
}

.submit-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  position: relative;
  z-index: 2;
}

.loading::after {
  content: '';
  width: 18px;
  height: 18px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid var(--text-inverse);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.success-message {
  margin-top: var(--space-lg);
  padding: var(--space-lg);
  background: rgba(34, 197, 94, 0.1);
  color: var(--secondary-color);
  border: 1px solid rgba(34, 197, 94, 0.2);
  border-radius: var(--radius-lg);
  text-align: center;
  font-weight: 600;
  font-size: 1rem;
  box-shadow: var(--shadow-sm);
}

[data-theme="dark"] .success-message {
  background: rgba(34, 197, 94, 0.15);
  border-color: rgba(34, 197, 94, 0.3);
  color: var(--secondary-light);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



/* Professional Responsive Design */
@media (max-width: 1200px) {
  .contact-content {
    gap: var(--space-2xl);
    grid-template-columns: 1fr 0.8fr;
  }
}

@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: var(--space-2xl);
  }

  .contact-form-container {
    padding: var(--space-xl);
  }

  .social-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-sm);
  }
}

@media (max-width: 768px) {
  .contact {
    padding: var(--space-2xl) 0;
  }

  .contact-content {
    gap: var(--space-xl);
  }

  .contact-item {
    padding: var(--space-lg);
    gap: var(--space-md);
  }

  .contact-icon {
    width: 52px;
    height: 52px;
    font-size: 1.2rem;
  }

  .office-hours,
  .social-links {
    padding: var(--space-lg);
  }

  .social-grid {
    grid-template-columns: 1fr;
    gap: var(--space-sm);
  }

  .contact-form-container {
    padding: var(--space-lg);
  }
}

@media (max-width: 480px) {
  .contact {
    padding: var(--space-xl) 0;
  }

  .contact-item {
    padding: var(--space-md);
    gap: var(--space-sm);
  }

  .contact-icon {
    width: 48px;
    height: 48px;
    font-size: 1.1rem;
  }

  .office-hours,
  .social-links {
    padding: var(--space-md);
  }

  .hour-item {
    flex-direction: column;
    gap: var(--space-xs);
    text-align: center;
  }

  .contact-form-container {
    padding: var(--space-md);
  }

  .form-group {
    margin-bottom: var(--space-lg);
  }

  .submit-btn {
    padding: var(--space-md) var(--space-lg);
    font-size: 1rem;
  }
}
