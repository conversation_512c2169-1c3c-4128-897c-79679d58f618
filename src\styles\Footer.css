.footer {
  background: var(--primary-color);
  color: white;
  padding: 4rem 0 2rem;
  margin-top: 4rem;
  transition: background-color 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.footer-content {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 3rem;
  margin-bottom: 3rem;
}

.footer-section h4 {
  color: white;
  margin-bottom: 1.5rem;
  font-size: 1.2rem;
  font-weight: 600;
}

/* Professional Logo Section */
.footer-logo {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: var(--space-xl);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.footer-logo:hover {
  transform: translateY(-2px);
}

.footer-logo-image {
  height: 60px;
  width: auto;
  max-width: 280px;
  color: white;
  transition: all var(--transition-fast);
}

.footer-logo:hover .footer-logo-image {
  filter: drop-shadow(0 2px 8px rgba(255, 255, 255, 0.3));
  transform: scale(1.02);
}

/* Legacy logo styles for fallback */
.footer-logo .logo-circle {
  width: 45px;
  height: 45px;
  border-radius: 50%;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-weight: 700;
  font-size: 0.9rem;
  font-family: 'Playfair Display', serif;
}

.footer-logo .logo-text {
  font-family: 'Playfair Display', serif;
  font-size: 1.4rem;
  font-weight: 600;
  color: white;
}

.footer-description {
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.6;
  margin-bottom: 2rem;
  font-size: 0.95rem;
}

.footer-social {
  display: flex;
  gap: 1rem;
}

.footer-social-link {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.1rem;
  text-decoration: none;
  transition: var(--transition-medium);
  backdrop-filter: blur(10px);
}

.footer-social-link:hover {
  background: rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

/* Quick Links */
.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.75rem;
}

.footer-link {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  font-size: 0.95rem;
  cursor: pointer;
  transition: var(--transition-fast);
  display: block;
}

.footer-link:hover {
  color: white;
  transform: translateX(5px);
}

/* Contact Info */
.footer-contact {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.contact-item {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.contact-icon {
  color: rgba(255, 255, 255, 0.6);
  margin-top: 0.2rem;
  flex-shrink: 0;
}

.contact-item a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: var(--transition-fast);
}

.contact-item a:hover {
  color: white;
}

.contact-item span {
  line-height: 1.4;
}

/* Research Areas */
.research-areas {
  list-style: none;
  padding: 0;
  margin: 0;
}

.research-areas li {
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 0.75rem;
  font-size: 0.9rem;
  position: relative;
  padding-left: 1rem;
}

.research-areas li::before {
  content: '▸';
  position: absolute;
  left: 0;
  color: rgba(255, 255, 255, 0.6);
}

.research-areas li:hover {
  color: white;
}

/* Footer Bottom */
.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 2rem;
}

.footer-bottom-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.copyright {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
}

.made-with {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.heart-icon {
  color: #e74c3c;
  animation: heartbeat 2s infinite;
}

@keyframes heartbeat {
  0%, 50%, 100% {
    transform: scale(1);
  }
  25%, 75% {
    transform: scale(1.1);
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2.5rem;
  }
  
  .footer-section:last-child {
    grid-column: span 3;
  }
}

@media (max-width: 768px) {
  .footer {
    padding: 3rem 0 2rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr 1fr;
    gap: 2rem;
  }
  
  .footer-section:first-child {
    grid-column: span 2;
  }
  
  .footer-section:last-child {
    grid-column: span 2;
  }
  
  .footer-bottom-content {
    flex-direction: column;
    text-align: center;
  }
}

@media (max-width: 480px) {
  .footer {
    padding: 2rem 0 1.5rem;
  }
  
  .footer-content {
    grid-template-columns: 1fr;
    gap: 2rem;
  }
  
  .footer-section:first-child,
  .footer-section:last-child {
    grid-column: span 1;
  }
  
  .footer-logo {
    justify-content: center;
  }
  
  .footer-description {
    text-align: center;
  }
  
  .footer-social {
    justify-content: center;
  }
  
  .footer-links,
  .research-areas {
    text-align: center;
  }
  
  .footer-contact {
    align-items: center;
  }
  
  .contact-item {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
  
  .footer-bottom-content {
    gap: 0.5rem;
  }
  
  .copyright,
  .made-with {
    font-size: 0.8rem;
  }
}
