/* Professional Header Styles */
.header {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
  z-index: 1000;
  transition: all var(--transition-medium);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.header.scrolled {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25px);
  -webkit-backdrop-filter: blur(25px);
  box-shadow: var(--shadow-md);
  border-bottom-color: rgba(0, 0, 0, 0.12);
}

/* Dark theme header styles */
[data-theme="dark"] .header {
  background: rgba(17, 24, 39, 0.85);
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

[data-theme="dark"] .header.scrolled {
  background: rgba(17, 24, 39, 0.95);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -1px rgba(0, 0, 0, 0.3);
  border-bottom-color: rgba(255, 255, 255, 0.12);
}

/* Professional Header Content */
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-lg) 0;
  min-height: 80px;
}

/* Professional Logo Design */
.logo {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
}

.logo:hover {
  transform: scale(1.02);
}

.logo-circle {
  width: 52px;
  height: 52px;
  border-radius: var(--radius-full);
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  font-weight: 700;
  font-size: 1rem;
  font-family: 'Playfair Display', serif;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.logo-circle::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, rgba(255,255,255,0.2) 0%, transparent 50%);
  border-radius: inherit;
}

.header-logo-image {
  height: clamp(32px, 4vw, 48px);
  width: auto;
  max-width: clamp(200px, 25vw, 320px);
  color: var(--text-primary);
  transition: all var(--transition-fast);
  object-fit: contain;
  display: block;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.logo:hover .header-logo-image {
  filter: drop-shadow(0 2px 8px rgba(59, 130, 246, 0.3));
  transform: scale(1.02);
}

[data-theme="dark"] .header-logo-image {
  color: var(--text-primary);
}

[data-theme="dark"] .logo:hover .header-logo-image {
  filter: drop-shadow(0 2px 8px rgba(100, 181, 246, 0.3));
}

/* Legacy logo text styles for fallback */
.logo-text {
  font-family: 'Playfair Display', serif;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--text-primary);
  letter-spacing: -0.02em;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Professional Navigation */
.nav-desktop {
  display: flex;
  align-items: center;
}

.nav-list {
  display: flex;
  list-style: none;
  gap: var(--space-sm);
  margin: 0;
  padding: 0;
  align-items: center;
}

.nav-link {
  position: relative;
  padding: var(--space-md) var(--space-lg);
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 0.95rem;
  text-decoration: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  white-space: nowrap;
  letter-spacing: -0.01em;
}

.nav-link:hover {
  color: var(--primary-color);
  background: rgba(59, 130, 246, 0.08);
  transform: translateY(-1px);
}

.nav-link.active {
  color: var(--primary-color);
  background: rgba(59, 130, 246, 0.12);
  font-weight: 600;
}

.nav-indicator {
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 24px;
  height: 3px;
  background: var(--primary-color);
  border-radius: var(--radius-full);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

/* Professional Mobile Menu */
.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 1.5rem;
  color: var(--text-primary);
  cursor: pointer;
  padding: var(--space-sm);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.mobile-menu-toggle:hover {
  background: rgba(59, 130, 246, 0.08);
  color: var(--primary-color);
}

.mobile-menu {
  display: none;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-top: 1px solid var(--border-light);
  box-shadow: var(--shadow-lg);
}

[data-theme="dark"] .mobile-menu {
  background: rgba(17, 24, 39, 0.95);
  border-top-color: rgba(255, 255, 255, 0.1);
}

.mobile-nav-list {
  list-style: none;
  padding: var(--space-lg) 0;
  margin: 0;
}

.mobile-nav-link {
  display: block;
  width: 100%;
  padding: var(--space-lg) var(--space-xl);
  color: var(--text-secondary);
  font-weight: 500;
  font-size: 1.1rem;
  text-decoration: none;
  text-align: left;
  cursor: pointer;
  transition: all var(--transition-fast);
  border-left: 3px solid transparent;
}

.mobile-nav-link:hover {
  background: rgba(59, 130, 246, 0.08);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  padding-left: calc(var(--space-xl) + var(--space-sm));
}

.mobile-nav-link.active {
  background: rgba(59, 130, 246, 0.12);
  color: var(--primary-color);
  font-weight: 600;
  border-left-color: var(--primary-color);
  padding-left: calc(var(--space-xl) + var(--space-sm));
}

/* Professional Responsive Design */
@media (min-width: 1400px) {
  .header-logo-image {
    height: clamp(40px, 4.5vw, 56px);
    max-width: clamp(240px, 28vw, 400px);
  }
}

@media (max-width: 1024px) {
  .nav-list {
    gap: var(--space-xs);
  }

  .nav-link {
    padding: var(--space-sm) var(--space-md);
    font-size: 0.9rem;
  }
}

@media (max-width: 768px) {
  .nav-desktop {
    display: none;
  }

  .mobile-menu-toggle {
    display: block;
  }

  .mobile-menu {
    display: block;
  }

  .header-content {
    padding: var(--space-md) 0;
    min-height: 70px;
  }

  .header-logo-image {
    height: clamp(28px, 3.5vw, 40px);
    max-width: clamp(160px, 22vw, 280px);
  }

  .logo-text {
    font-size: 1.3rem;
  }

  .logo-circle {
    width: 48px;
    height: 48px;
    font-size: 0.9rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    padding: var(--space-sm) 0;
    min-height: 60px;
  }

  .header-logo-image {
    height: clamp(24px, 3vw, 36px);
    max-width: clamp(140px, 20vw, 240px);
  }

  .logo-text {
    display: none;
  }

  .logo-circle {
    width: 44px;
    height: 44px;
    font-size: 0.85rem;
  }

  .mobile-nav-link {
    padding: var(--space-md) var(--space-lg);
    font-size: 1rem;
  }
}

@media (max-width: 320px) {
  .header-logo-image {
    height: clamp(20px, 2.5vw, 32px);
    max-width: clamp(120px, 18vw, 200px);
  }
}

/* Dark Theme Enhancements */
[data-theme="dark"] .nav-link {
  color: var(--text-secondary);
}

[data-theme="dark"] .nav-link:hover {
  color: var(--primary-light);
  background: rgba(100, 181, 246, 0.1);
}

[data-theme="dark"] .nav-link.active {
  color: var(--primary-light);
  background: rgba(100, 181, 246, 0.15);
}

[data-theme="dark"] .mobile-menu-toggle {
  color: var(--text-primary);
}

[data-theme="dark"] .mobile-menu-toggle:hover {
  background: rgba(100, 181, 246, 0.1);
  color: var(--primary-light);
}

[data-theme="dark"] .mobile-nav-link {
  color: var(--text-secondary);
}

[data-theme="dark"] .mobile-nav-link:hover {
  background: rgba(100, 181, 246, 0.1);
  color: var(--primary-light);
  border-left-color: var(--primary-light);
}

[data-theme="dark"] .mobile-nav-link.active {
  background: rgba(100, 181, 246, 0.15);
  color: var(--primary-light);
  border-left-color: var(--primary-light);
}

/* Professional Animation Enhancements */
.header {
  animation: slideDown 0.6s ease-out;
}

@keyframes slideDown {
  from {
    transform: translateY(-100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.nav-link::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(59, 130, 246, 0.05);
  border-radius: inherit;
  transform: scale(0);
  transition: transform var(--transition-fast);
  z-index: -1;
}

.nav-link:hover::before {
  transform: scale(1);
}

/* Professional Resume Link Styling */
.nav-link.resume-link {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--text-inverse) !important;
  border-radius: var(--radius-lg);
  font-weight: 600;
  box-shadow: var(--shadow-sm);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.nav-link.resume-link:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.mobile-nav-link.resume-link {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  color: var(--text-inverse) !important;
  border-radius: var(--radius-lg);
  font-weight: 600;
  margin: var(--space-sm) var(--space-lg);
  text-align: center;
  box-shadow: var(--shadow-sm);
}

.mobile-nav-link.resume-link:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
  transform: translateX(0) !important;
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .nav-link.resume-link {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
  color: var(--text-primary) !important;
}

[data-theme="dark"] .mobile-nav-link.resume-link {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--secondary-light) 100%);
  color: var(--text-primary) !important;
}
