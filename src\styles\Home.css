/* Professional Home Page */
.home-page {
  min-height: 100vh;
  background: var(--background-primary);
}

/* Professional Hero Section */
.hero-section {
  min-height: 100vh;
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(248, 250, 252, 1) 50%,
    rgba(241, 245, 249, 1) 100%);
  transition: all var(--transition-medium);
  padding-top: 80px; /* Account for fixed header */
}

/* Professional Dark Theme Hero */
[data-theme="dark"] .hero-section {
  background: linear-gradient(135deg,
    rgba(17, 24, 39, 1) 0%,
    rgba(31, 41, 55, 1) 50%,
    rgba(55, 65, 81, 1) 100%);
}

/* Professional Hero Background */
.hero-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  overflow: hidden;
}

.hero-particles {
  position: absolute;
  width: 100%;
  height: 100%;
  background-image:
    radial-gradient(circle at 25% 75%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 25%, rgba(16, 185, 129, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  animation: float 30s ease-in-out infinite;
}

/* Professional floating animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  33% {
    transform: translateY(-20px) rotate(120deg);
    opacity: 0.9;
  }
  66% {
    transform: translateY(-10px) rotate(240deg);
    opacity: 0.8;
  }
}

/* Professional Hero Content */
.hero-content {
  display: grid;
  grid-template-columns: 1.2fr 0.8fr;
  gap: var(--space-3xl);
  align-items: center;
  position: relative;
  z-index: 1;
  padding: var(--space-3xl) 0;
  max-width: 1400px;
  margin: 0 auto;
}

.hero-text {
  max-width: 700px;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: 800;
  line-height: 1.1;
  margin-bottom: var(--space-xl);
  color: var(--text-primary);
  letter-spacing: -0.02em;
}

.hero-title .gradient-text {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  position: relative;
}

.hero-subtitle {
  font-size: clamp(1.25rem, 2.5vw, 1.75rem);
  color: var(--primary-color);
  font-weight: 600;
  margin-bottom: var(--space-lg);
  font-family: 'Inter', sans-serif;
  letter-spacing: -0.01em;
}

.hero-description {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  color: var(--text-secondary);
  line-height: 1.8;
  margin-bottom: var(--space-2xl);
  max-width: 600px;
  font-weight: 400;
}

/* Professional Hero Buttons */
.hero-buttons {
  display: flex;
  gap: var(--space-lg);
  margin-bottom: var(--space-2xl);
  flex-wrap: wrap;
  align-items: center;
}

.hero-buttons .btn {
  display: inline-flex;
  align-items: center;
  gap: var(--space-sm);
  font-size: 1.1rem;
  font-weight: 600;
  padding: var(--space-lg) var(--space-2xl);
  text-decoration: none;
  border-radius: var(--radius-xl);
  transition: all var(--transition-medium);
  letter-spacing: -0.01em;
  min-width: 160px;
  justify-content: center;
}

.hero-buttons .btn-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%);
  color: var(--text-inverse);
  box-shadow: var(--shadow-lg);
}

.hero-buttons .btn-primary:hover {
  transform: translateY(-3px);
  box-shadow: var(--shadow-xl);
  filter: brightness(1.1);
}

.hero-buttons .btn-outline {
  background: rgba(255, 255, 255, 0.9);
  color: var(--primary-color);
  border: 2px solid var(--primary-color);
  backdrop-filter: blur(10px);
}

.hero-buttons .btn-outline:hover {
  background: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* Professional Social Links */
.hero-social {
  display: flex;
  gap: var(--space-md);
  align-items: center;
}

.social-link {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-full);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-size: 1.4rem;
  box-shadow: var(--shadow-md);
  transition: all var(--transition-medium);
  text-decoration: none;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.social-link:hover {
  transform: translateY(-4px) scale(1.05);
  box-shadow: var(--shadow-xl);
  color: var(--secondary-color);
  background: rgba(255, 255, 255, 1);
}

[data-theme="dark"] .social-link {
  background: rgba(31, 41, 55, 0.9);
  color: var(--text-primary);
  border-color: rgba(75, 85, 99, 0.3);
}

[data-theme="dark"] .social-link:hover {
  background: rgba(55, 65, 81, 1);
  color: var(--primary-light);
}

/* Professional Hero Image */
.hero-image {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.image-container {
  position: relative;
  width: 450px;
  height: 450px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.profile-image {
  width: 380px;
  height: 380px;
  border-radius: var(--radius-full);
  overflow: hidden;
  position: relative;
  z-index: 3;
  box-shadow: var(--shadow-2xl);
  background: var(--background-primary);
  padding: 12px;
  border: 4px solid rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
}

.profile-photo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-full);
  transition: all var(--transition-medium);
  filter: brightness(1.05) contrast(1.1);
}

.profile-photo:hover {
  transform: scale(1.02);
  filter: brightness(1.1) contrast(1.15);
}

/* Professional Image Placeholder */
.image-placeholder {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-full);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--text-inverse);
  position: relative;
  z-index: 3;
  box-shadow: var(--shadow-2xl);
}

.placeholder-content {
  text-align: center;
}

.placeholder-icon {
  font-size: 5rem;
  margin-bottom: var(--space-lg);
  opacity: 0.9;
}

.placeholder-content p {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  letter-spacing: -0.01em;
}

/* Professional Image Decorations */
.image-decoration {
  position: absolute;
  top: -30px;
  left: -30px;
  width: calc(100% + 60px);
  height: calc(100% + 60px);
  border-radius: var(--radius-full);
  border: 3px solid rgba(59, 130, 246, 0.2);
  z-index: 1;
  animation: rotate 30s linear infinite;
}

.image-decoration::before {
  content: '';
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  bottom: 20px;
  border-radius: var(--radius-full);
  border: 2px solid rgba(16, 185, 129, 0.15);
  animation: rotate 25s linear infinite reverse;
}

.image-decoration::after {
  content: '';
  position: absolute;
  top: 40px;
  left: 40px;
  right: 40px;
  bottom: 40px;
  border-radius: var(--radius-full);
  border: 1px solid rgba(139, 92, 246, 0.1);
  animation: rotate 20s linear infinite;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Professional Navigation Section */
.navigation-section {
  padding: var(--space-3xl) 0 6rem 0;
  background: var(--background-secondary);
  position: relative;
}

[data-theme="dark"] .navigation-section {
  background: rgba(17, 24, 39, 0.5);
}

.navigation-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
}

.navigation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-xl);
  margin-top: var(--space-3xl);
}

.navigation-card {
  height: 100%;
  transition: all var(--transition-medium);
}

.navigation-card:hover {
  transform: translateY(-8px);
}

.card-link {
  display: block;
  height: 100%;
  text-decoration: none;
  color: inherit;
}

.card-gradient {
  height: 100%;
  border-radius: var(--radius-2xl);
  padding: var(--space-2xl);
  position: relative;
  overflow: hidden;
  transition: all var(--transition-medium);
  box-shadow: var(--shadow-lg);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.card-gradient::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
  z-index: 1;
}

.card-content {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  color: var(--text-inverse);
}

.card-icon {
  font-size: 3.5rem;
  margin-bottom: var(--space-lg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.card-title {
  font-size: 1.75rem;
  font-weight: 700;
  margin-bottom: var(--space-md);
  color: var(--text-inverse);
  font-family: 'Playfair Display', serif;
  letter-spacing: -0.01em;
}

.card-description {
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: var(--space-xl);
  flex-grow: 1;
  color: rgba(255, 255, 255, 0.95);
  font-weight: 400;
}

.card-arrow {
  font-size: 1.5rem;
  align-self: flex-end;
  transition: all var(--transition-fast);
  background: rgba(255, 255, 255, 0.2);
  width: 48px;
  height: 48px;
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
}

.navigation-card:hover .card-arrow {
  transform: translateX(8px) scale(1.1);
  background: rgba(255, 255, 255, 0.3);
}

.navigation-card:hover .card-gradient {
  transform: scale(1.02);
  box-shadow: var(--shadow-2xl);
}

/* Professional Stats Section */
.stats-section {
  padding: var(--space-3xl) 0;
  background: var(--background-primary);
  position: relative;
}

[data-theme="dark"] .stats-section {
  background: rgba(31, 41, 55, 0.3);
}

.stats-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--space-xl);
  max-width: 1000px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  padding: var(--space-2xl);
  background: var(--background-card);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-medium);
  border: 1px solid var(--border-light);
  position: relative;
  overflow: hidden;
}

.stat-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.stat-item:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.stat-number {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  color: var(--primary-color);
  margin-bottom: var(--space-sm);
  font-family: 'Playfair Display', serif;
  letter-spacing: -0.02em;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-label {
  font-size: 1.2rem;
  color: var(--text-secondary);
  font-weight: 600;
  letter-spacing: -0.01em;
  text-transform: uppercase;
  font-size: 0.95rem;
}

[data-theme="dark"] .stat-item {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .stat-item:hover {
  background: rgba(55, 65, 81, 0.8);
  border-color: var(--primary-light);
}

/* Professional Responsive Design */
@media (max-width: 1200px) {
  .hero-content {
    gap: var(--space-2xl);
    grid-template-columns: 1.1fr 0.9fr;
  }

  .image-container {
    width: 400px;
    height: 400px;
  }

  .profile-image {
    width: 340px;
    height: 340px;
  }
}

@media (max-width: 1024px) {
  .hero-content {
    gap: var(--space-2xl);
    grid-template-columns: 1fr;
    text-align: center;
    padding: var(--space-2xl) 0;
  }

  .hero-text {
    max-width: 800px;
    margin: 0 auto;
  }

  .image-container {
    width: 380px;
    height: 380px;
    margin: 0 auto;
  }

  .profile-image {
    width: 320px;
    height: 320px;
  }

  .navigation-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: var(--space-lg);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-lg);
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding-top: 70px;
  }

  .hero-content {
    padding: var(--space-xl) 0;
  }

  .hero-buttons {
    justify-content: center;
    gap: var(--space-md);
  }

  .hero-social {
    justify-content: center;
  }

  .image-container {
    width: 320px;
    height: 320px;
  }

  .profile-image {
    width: 280px;
    height: 280px;
  }

  .navigation-grid {
    grid-template-columns: 1fr;
    gap: var(--space-lg);
  }

  .card-gradient {
    padding: var(--space-xl);
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-md);
  }
}

@media (max-width: 480px) {
  .hero-section {
    padding-top: 60px;
  }

  .hero-content {
    padding: var(--space-lg) 0;
  }

  .hero-buttons {
    flex-direction: column;
    align-items: center;
    gap: var(--space-md);
  }

  .hero-buttons .btn {
    width: 100%;
    max-width: 280px;
  }

  .image-container {
    width: 280px;
    height: 280px;
  }

  .profile-image {
    width: 240px;
    height: 240px;
    padding: 8px;
  }

  .placeholder-icon {
    font-size: 3.5rem;
  }

  .social-link {
    width: 48px;
    height: 48px;
    font-size: 1.2rem;
  }

  .card-gradient {
    padding: var(--space-lg);
  }

  .card-icon {
    font-size: 3rem;
  }

  .card-title {
    font-size: 1.5rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--space-md);
  }

  .stat-item {
    padding: var(--space-lg);
  }
}
