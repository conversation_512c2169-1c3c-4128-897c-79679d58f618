/* Page Layout Styles */
.page-container {
  min-height: 100vh;
  padding-top: 80px; /* Account for fixed header */
}

.page-header {
  background: var(--gradient-primary);
  color: white;
  padding: 3rem 0;
  margin-bottom: 2rem;
}

.back-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  font-size: 0.9rem;
  margin-bottom: 1.5rem;
  transition: var(--transition-fast);
}

.back-link:hover {
  color: white;
  transform: translateX(-3px);
}

.page-title {
  font-size: 3rem;
  font-weight: 700;
  margin-bottom: 1rem;
  color: white;
  font-family: 'Playfair Display', serif;
}

.page-subtitle {
  font-size: 1.2rem;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 300;
}

/* Responsive Design */
@media (max-width: 768px) {
  .page-container {
    padding-top: 70px;
  }
  
  .page-header {
    padding: 2rem 0;
  }
  
  .page-title {
    font-size: 2.2rem;
  }
  
  .page-subtitle {
    font-size: 1.1rem;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.8rem;
  }
  
  .page-subtitle {
    font-size: 1rem;
  }
}
