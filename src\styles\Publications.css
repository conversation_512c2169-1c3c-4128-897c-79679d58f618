/* Professional Publications Page */
.publications {
  background: var(--background-primary);
  padding: var(--space-3xl) 0;
  position: relative;
}

[data-theme="dark"] .publications {
  background: var(--background-dark);
}

.publications::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
}

/* Professional Publication Stats */
.publication-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  gap: var(--space-xl);
  margin-bottom: var(--space-3xl);
  padding: var(--space-2xl);
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: var(--radius-2xl);
  color: var(--text-inverse);
  box-shadow: var(--shadow-lg);
  position: relative;
  overflow: hidden;
}

.publication-stats::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%, rgba(0, 0, 0, 0.1) 100%);
  z-index: 1;
}

.stat-item {
  text-align: center;
  position: relative;
  z-index: 2;
}

.stat-number {
  font-size: clamp(2.5rem, 4vw, 3.5rem);
  font-weight: 800;
  font-family: 'Playfair Display', serif;
  margin-bottom: var(--space-sm);
  letter-spacing: -0.02em;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.stat-label {
  font-size: 1.2rem;
  font-weight: 600;
  opacity: 0.95;
  color: var(--text-inverse);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-size: 1rem;
}

/* Professional Controls */
.publication-controls {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xl);
  align-items: center;
  margin-bottom: var(--space-2xl);
  padding: var(--space-xl);
  background: var(--background-card);
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-medium);
}

[data-theme="dark"] .publication-controls {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  backdrop-filter: blur(10px);
}

.search-box {
  position: relative;
  flex: 1;
  min-width: 320px;
}

.search-icon {
  position: absolute;
  left: var(--space-lg);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-muted);
  font-size: 1.1rem;
}

.search-input {
  width: 100%;
  padding: var(--space-md) var(--space-lg) var(--space-md) calc(var(--space-lg) * 2.5);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  font-size: 1rem;
  font-weight: 500;
  background: var(--background-primary);
  color: var(--text-primary);
  transition: all var(--transition-fast);
}

.search-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: var(--text-muted);
  font-weight: 400;
}

[data-theme="dark"] .search-input {
  background: rgba(31, 41, 55, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

.category-filters {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  flex-wrap: wrap;
}

.filter-icon {
  color: var(--text-muted);
  font-size: 1.2rem;
}

.filter-btn {
  padding: var(--space-sm) var(--space-lg);
  border: 2px solid var(--border-light);
  border-radius: var(--radius-xl);
  background: var(--background-primary);
  color: var(--text-secondary);
  font-weight: 600;
  font-size: 0.95rem;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
}

.filter-btn:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-1px);
}

.filter-btn.active {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: var(--text-inverse);
  box-shadow: var(--shadow-md);
}

[data-theme="dark"] .filter-btn {
  background: rgba(31, 41, 55, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  color: var(--text-secondary);
}

[data-theme="dark"] .filter-btn:hover {
  border-color: var(--primary-light);
  color: var(--primary-light);
}

[data-theme="dark"] .filter-btn.active {
  background: var(--primary-light);
  border-color: var(--primary-light);
  color: var(--text-primary);
}
}

.filter-btn:hover {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
}

.filter-btn.active {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
}

/* Publications List */
.publications-list {
  display: grid;
  gap: 2rem;
}

.publication-card {
  background: white;
  padding: 2.5rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: var(--transition-medium);
}

.publication-card:hover {
  box-shadow: var(--shadow-medium);
}

.publication-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.publication-icon {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.2rem;
}

.publication-meta {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.impact-badge {
  padding: 0.3rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.impact-badge.high-impact {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.impact-badge.medium-impact {
  background: rgba(241, 196, 15, 0.1);
  color: #f1c40f;
}

.impact-badge.low-impact {
  background: rgba(231, 76, 60, 0.1);
  color: #e74c3c;
}

.publication-year {
  color: var(--text-secondary);
  font-weight: 500;
}

.publication-title {
  color: var(--primary-color);
  font-size: 1.4rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.publication-authors {
  color: var(--text-secondary);
  margin-bottom: 0.5rem;
  font-weight: 500;
}

.publication-journal {
  color: var(--secondary-color);
  font-style: italic;
  margin-bottom: 1.5rem;
}

.publication-abstract {
  background: var(--background-dark);
  padding: 1.5rem;
  border-radius: 10px;
  margin-bottom: 2rem;
  position: relative;
}

.quote-icon {
  position: absolute;
  top: 1rem;
  left: 1rem;
  color: var(--secondary-color);
  opacity: 0.8;
}

.publication-abstract p {
  margin: 0;
  padding-left: 2rem;
  color: var(--text-secondary);
  line-height: 1.6;
  font-style: italic;
}

.publication-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.publication-stats {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.citations {
  color: var(--primary-color);
  font-weight: 600;
}

.doi {
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-family: monospace;
}

.publication-link {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.no-results {
  text-align: center;
  padding: 3rem;
  color: var(--text-secondary);
  font-size: 1.1rem;
}

/* Theme-specific fixes for text visibility */
[data-theme="dark"] .filter-btn {
  background: var(--background-secondary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .filter-btn:hover {
  border-color: var(--secondary-color);
  color: var(--secondary-color);
}

[data-theme="dark"] .filter-btn.active {
  background: var(--secondary-color);
  border-color: var(--secondary-color);
  color: white;
}

[data-theme="dark"] .search-input {
  background: var(--background-secondary);
  color: var(--text-primary);
  border-color: var(--border-color);
}

[data-theme="dark"] .search-input::placeholder {
  color: var(--text-muted);
}

[data-theme="dark"] .publication-card {
  background: var(--background-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .publication-card * {
  opacity: 1 !important;
}

[data-theme="dark"] .quote-icon {
  opacity: 0.8 !important;
  color: var(--secondary-color) !important;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .publication-stats {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1.5rem;
    padding: 2.5rem;
  }
  
  .stat-number {
    font-size: 2.5rem;
  }
}

@media (max-width: 768px) {
  .publications {
    padding: 4rem 0;
  }
  
  .publication-stats {
    grid-template-columns: repeat(2, 1fr);
    padding: 2rem;
  }
  
  .stat-number {
    font-size: 2rem;
  }
  
  .publication-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 1.5rem;
    padding: 1.5rem;
  }
  
  .search-box {
    min-width: auto;
  }
  
  .category-filters {
    justify-content: center;
  }
  
  .publication-card {
    padding: 2rem;
  }
  
  .publication-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .publication-footer {
    flex-direction: column;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .publication-stats {
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    padding: 1.5rem;
  }
  
  .stat-number {
    font-size: 1.8rem;
  }
  
  .stat-label {
    font-size: 1rem;
  }
  
  .publication-controls {
    padding: 1rem;
  }
  
  .category-filters {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filter-btn {
    text-align: center;
  }
  
  .publication-card {
    padding: 1.5rem;
  }
  
  .publication-title {
    font-size: 1.2rem;
  }
  
  .publication-abstract {
    padding: 1rem;
  }
  
  .publication-abstract p {
    padding-left: 1.5rem;
  }
}
