/* Professional Research Page */
.research {
  background: var(--background-primary);
  padding: var(--space-3xl) 0;
  position: relative;
}

[data-theme="dark"] .research {
  background: var(--background-dark);
}

.research::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--border-light) 50%, transparent 100%);
}

.subsection-title {
  font-size: clamp(1.75rem, 3vw, 2.25rem);
  color: var(--text-primary);
  margin-bottom: var(--space-2xl);
  text-align: center;
  font-weight: 700;
  letter-spacing: -0.02em;
  position: relative;
}

.subsection-title::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  border-radius: var(--radius-full);
}

/* Professional Research Areas */
.research-areas {
  margin-bottom: var(--space-3xl);
}

.areas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: var(--space-xl);
}

.area-card {
  background: var(--background-card);
  padding: var(--space-2xl);
  border-radius: var(--radius-2xl);
  text-align: center;
  box-shadow: var(--shadow-md);
  border: 1px solid var(--border-light);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.area-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.area-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-color);
}

.area-icon {
  font-size: 3.5rem;
  color: var(--secondary-color);
  margin-bottom: var(--space-lg);
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.area-card h4 {
  color: var(--text-primary);
  margin-bottom: var(--space-md);
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: -0.01em;
}

.area-card p {
  color: var(--text-secondary);
  line-height: 1.7;
  margin: 0;
  font-size: 1.1rem;
}

[data-theme="dark"] .area-card {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
  backdrop-filter: blur(10px);
}

[data-theme="dark"] .area-card:hover {
  background: rgba(55, 65, 81, 0.8);
  border-color: var(--primary-light);
}

/* Professional Current Projects */
.current-projects {
  margin-bottom: var(--space-3xl);
}

.projects-container {
  display: grid;
  grid-template-columns: 350px 1fr;
  gap: var(--space-2xl);
  background: var(--background-card);
  border-radius: var(--radius-2xl);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
  border: 1px solid var(--border-light);
}

[data-theme="dark"] .projects-container {
  background: rgba(55, 65, 81, 0.5);
  border-color: rgba(75, 85, 99, 0.3);
}

.project-tabs {
  background: var(--background-secondary);
  padding: var(--space-xl) 0;
  display: flex;
  flex-direction: column;
  gap: var(--space-sm);
  border-right: 1px solid var(--border-light);
}

[data-theme="dark"] .project-tabs {
  background: rgba(31, 41, 55, 0.8);
  border-right-color: rgba(75, 85, 99, 0.3);
}

.project-tab {
  display: flex;
  align-items: center;
  gap: var(--space-md);
  padding: var(--space-lg) var(--space-xl);
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: all var(--transition-fast);
  color: var(--text-secondary);
  border-left: 4px solid transparent;
  font-weight: 500;
}

.project-tab:hover {
  background: rgba(59, 130, 246, 0.08);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
}

.project-tab.active {
  background: rgba(59, 130, 246, 0.12);
  color: var(--primary-color);
  border-left-color: var(--primary-color);
  font-weight: 600;
}
  border-left-color: var(--secondary-color);
  color: var(--secondary-color);
}

.tab-icon {
  font-size: 1.5rem;
}

.tab-title {
  font-weight: 500;
  font-size: 0.95rem;
}

.project-content {
  padding: 3rem;
}

.project-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.project-icon {
  font-size: 3rem;
}

.project-info h4 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.project-status {
  padding: 0.3rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.project-status.ongoing {
  background: rgba(52, 152, 219, 0.1);
  color: var(--secondary-color);
}

.project-status.completed {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.project-details {
  margin-bottom: 2.5rem;
}

.detail-item {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.detail-item strong {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

/* Research Impact */
.research-impact {
  margin-top: 3rem;
}

.impact-card {
  background: var(--gradient-primary);
  color: white;
  padding: 3rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 2rem;
  box-shadow: var(--shadow-medium);
}

.impact-icon {
  font-size: 3rem;
  opacity: 1;
}

.impact-content h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.impact-content p {
  color: white;
  line-height: 1.6;
  margin: 0;
  font-size: 1.1rem;
}

/* Theme-specific fixes for text visibility */
[data-theme="dark"] .area-card {
  background: var(--background-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .area-card *,
[data-theme="dark"] .project-content *,
[data-theme="dark"] .impact-card * {
  opacity: 1 !important;
}

[data-theme="dark"] .project-tab {
  color: var(--text-primary);
  background: var(--background-secondary);
}

[data-theme="dark"] .project-tab:hover {
  background: rgba(100, 181, 246, 0.1);
}

[data-theme="dark"] .project-tab.active {
  background: rgba(100, 181, 246, 0.1);
  color: var(--secondary-color);
}

[data-theme="dark"] .project-status.ongoing {
  background: rgba(100, 181, 246, 0.2);
  color: var(--secondary-color);
}

[data-theme="dark"] .project-status.completed {
  background: rgba(129, 199, 132, 0.2);
  color: #81c784;
}

/* Patents Section */
.patents-section {
  margin-bottom: 5rem;
}

.patents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.patent-card {
  background: var(--background-primary);
  padding: 2.5rem 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.patent-card:hover {
  box-shadow: var(--shadow-medium);
}

.patent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.patent-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.published {
  background: rgba(39, 174, 96, 0.2);
  color: #27ae60;
}

.status-badge.completed {
  background: rgba(39, 174, 96, 0.2);
  color: #27ae60;
}

.patent-title {
  color: var(--primary-color);
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.patent-number {
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 1rem;
  font-family: monospace;
}

.patent-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.patent-details .detail-item {
  margin-bottom: 0.8rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.patent-details .detail-item strong {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

/* Research Projects Section */
.research-projects-section {
  margin-bottom: 5rem;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.research-project-card {
  background: var(--background-primary);
  padding: 2.5rem 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.research-project-card:hover {
  box-shadow: var(--shadow-medium);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.project-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.project-title {
  color: var(--primary-color);
  font-size: 1.3rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-details .detail-item {
  margin-bottom: 0.8rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.project-details .detail-item strong {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

/* Theme-specific fixes for new sections */
[data-theme="dark"] .patent-card,
[data-theme="dark"] .research-project-card {
  background: var(--background-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .patent-card *,
[data-theme="dark"] .research-project-card * {
  opacity: 1 !important;
}

[data-theme="dark"] .status-badge.published,
[data-theme="dark"] .status-badge.completed {
  background: rgba(129, 199, 132, 0.2);
  color: #81c784;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .projects-container {
    grid-template-columns: 250px 1fr;
    gap: 2rem;
  }
  
  .project-content {
    padding: 2.5rem;
  }
}

@media (max-width: 768px) {
  .research {
    padding: 4rem 0;
  }
  
  .areas-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .area-card {
    padding: 2rem 1.5rem;
  }
  
  .projects-container {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .project-tabs {
    flex-direction: row;
    overflow-x: auto;
    padding: 1rem;
    gap: 0;
  }
  
  .project-tab {
    flex-direction: column;
    min-width: 120px;
    padding: 1rem;
    text-align: center;
    border-left: none;
    border-bottom: 3px solid transparent;
  }
  
  .project-tab.active {
    border-left: none;
    border-bottom-color: var(--secondary-color);
  }
  
  .tab-title {
    font-size: 0.8rem;
  }
  
  .project-content {
    padding: 2rem;
  }
  
  .impact-card {
    flex-direction: column;
    text-align: center;
    padding: 2.5rem 2rem;
  }
}

@media (max-width: 480px) {
  .area-card {
    padding: 1.5rem 1rem;
  }
  
  .area-icon {
    font-size: 2.5rem;
  }
  
  .project-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .project-content {
    padding: 1.5rem;
  }
  
  .impact-card {
    padding: 2rem 1.5rem;
  }
  
  .impact-icon {
    font-size: 2.5rem;
  }
}
