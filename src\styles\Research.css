.research {
  background: var(--background-dark);
  padding: 6rem 0;
}

.subsection-title {
  font-size: 1.8rem;
  color: var(--primary-color);
  margin-bottom: 2.5rem;
  text-align: center;
}

/* Research Areas */
.research-areas {
  margin-bottom: 5rem;
}

.areas-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 2rem;
}

.area-card {
  background: var(--background-primary);
  padding: 2.5rem 2rem;
  border-radius: 15px;
  text-align: center;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.area-card:hover {
  box-shadow: var(--shadow-medium);
}

.area-icon {
  font-size: 3rem;
  color: var(--secondary-color);
  margin-bottom: 1.5rem;
}

.area-card h4 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.area-card p {
  color: var(--text-secondary);
  line-height: 1.6;
  margin: 0;
}

/* Current Projects */
.current-projects {
  margin-bottom: 4rem;
}

.projects-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 3rem;
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow-medium);
}

.project-tabs {
  background: var(--background-dark);
  padding: 2rem 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.project-tab {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.5rem 2rem;
  background: none;
  border: none;
  text-align: left;
  cursor: pointer;
  transition: var(--transition-fast);
  color: var(--text-primary);
  border-left: 3px solid transparent;
}

.project-tab:hover {
  background: rgba(52, 152, 219, 0.1);
}

.project-tab.active {
  background: rgba(52, 152, 219, 0.1);
  border-left-color: var(--secondary-color);
  color: var(--secondary-color);
}

.tab-icon {
  font-size: 1.5rem;
}

.tab-title {
  font-weight: 500;
  font-size: 0.95rem;
}

.project-content {
  padding: 3rem;
}

.project-header {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.project-icon {
  font-size: 3rem;
}

.project-info h4 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.project-status {
  padding: 0.3rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.project-status.ongoing {
  background: rgba(52, 152, 219, 0.1);
  color: var(--secondary-color);
}

.project-status.completed {
  background: rgba(39, 174, 96, 0.1);
  color: #27ae60;
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.7;
  margin-bottom: 2rem;
  font-size: 1.1rem;
}

.project-details {
  margin-bottom: 2.5rem;
}

.detail-item {
  margin-bottom: 1rem;
  color: var(--text-secondary);
}

.detail-item strong {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

.project-link {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

/* Research Impact */
.research-impact {
  margin-top: 3rem;
}

.impact-card {
  background: var(--gradient-primary);
  color: white;
  padding: 3rem;
  border-radius: 20px;
  display: flex;
  align-items: center;
  gap: 2rem;
  box-shadow: var(--shadow-medium);
}

.impact-icon {
  font-size: 3rem;
  opacity: 1;
}

.impact-content h4 {
  color: white;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.impact-content p {
  color: white;
  line-height: 1.6;
  margin: 0;
  font-size: 1.1rem;
}

/* Theme-specific fixes for text visibility */
[data-theme="dark"] .area-card {
  background: var(--background-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .area-card *,
[data-theme="dark"] .project-content *,
[data-theme="dark"] .impact-card * {
  opacity: 1 !important;
}

[data-theme="dark"] .project-tab {
  color: var(--text-primary);
  background: var(--background-secondary);
}

[data-theme="dark"] .project-tab:hover {
  background: rgba(100, 181, 246, 0.1);
}

[data-theme="dark"] .project-tab.active {
  background: rgba(100, 181, 246, 0.1);
  color: var(--secondary-color);
}

[data-theme="dark"] .project-status.ongoing {
  background: rgba(100, 181, 246, 0.2);
  color: var(--secondary-color);
}

[data-theme="dark"] .project-status.completed {
  background: rgba(129, 199, 132, 0.2);
  color: #81c784;
}

/* Patents Section */
.patents-section {
  margin-bottom: 5rem;
}

.patents-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.patent-card {
  background: var(--background-primary);
  padding: 2.5rem 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.patent-card:hover {
  box-shadow: var(--shadow-medium);
}

.patent-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.patent-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-primary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.status-badge {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
  text-transform: uppercase;
}

.status-badge.published {
  background: rgba(39, 174, 96, 0.2);
  color: #27ae60;
}

.status-badge.completed {
  background: rgba(39, 174, 96, 0.2);
  color: #27ae60;
}

.patent-title {
  color: var(--primary-color);
  font-size: 1.3rem;
  margin-bottom: 0.5rem;
  line-height: 1.3;
}

.patent-number {
  color: var(--secondary-color);
  font-weight: 600;
  margin-bottom: 1rem;
  font-family: monospace;
}

.patent-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.patent-details .detail-item {
  margin-bottom: 0.8rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.patent-details .detail-item strong {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

/* Research Projects Section */
.research-projects-section {
  margin-bottom: 5rem;
}

.projects-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 2rem;
}

.research-project-card {
  background: var(--background-primary);
  padding: 2.5rem 2rem;
  border-radius: 15px;
  box-shadow: var(--shadow-light);
  border: 1px solid var(--border-color);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.research-project-card:hover {
  box-shadow: var(--shadow-medium);
}

.project-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.project-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: var(--gradient-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
}

.project-title {
  color: var(--primary-color);
  font-size: 1.3rem;
  margin-bottom: 1rem;
  line-height: 1.3;
}

.project-description {
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.project-details .detail-item {
  margin-bottom: 0.8rem;
  color: var(--text-secondary);
  font-size: 0.9rem;
}

.project-details .detail-item strong {
  color: var(--primary-color);
  margin-right: 0.5rem;
}

/* Theme-specific fixes for new sections */
[data-theme="dark"] .patent-card,
[data-theme="dark"] .research-project-card {
  background: var(--background-secondary);
  border-color: var(--border-color);
}

[data-theme="dark"] .patent-card *,
[data-theme="dark"] .research-project-card * {
  opacity: 1 !important;
}

[data-theme="dark"] .status-badge.published,
[data-theme="dark"] .status-badge.completed {
  background: rgba(129, 199, 132, 0.2);
  color: #81c784;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .projects-container {
    grid-template-columns: 250px 1fr;
    gap: 2rem;
  }
  
  .project-content {
    padding: 2.5rem;
  }
}

@media (max-width: 768px) {
  .research {
    padding: 4rem 0;
  }
  
  .areas-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .area-card {
    padding: 2rem 1.5rem;
  }
  
  .projects-container {
    grid-template-columns: 1fr;
    gap: 0;
  }
  
  .project-tabs {
    flex-direction: row;
    overflow-x: auto;
    padding: 1rem;
    gap: 0;
  }
  
  .project-tab {
    flex-direction: column;
    min-width: 120px;
    padding: 1rem;
    text-align: center;
    border-left: none;
    border-bottom: 3px solid transparent;
  }
  
  .project-tab.active {
    border-left: none;
    border-bottom-color: var(--secondary-color);
  }
  
  .tab-title {
    font-size: 0.8rem;
  }
  
  .project-content {
    padding: 2rem;
  }
  
  .impact-card {
    flex-direction: column;
    text-align: center;
    padding: 2.5rem 2rem;
  }
}

@media (max-width: 480px) {
  .area-card {
    padding: 1.5rem 1rem;
  }
  
  .area-icon {
    font-size: 2.5rem;
  }
  
  .project-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }
  
  .project-content {
    padding: 1.5rem;
  }
  
  .impact-card {
    padding: 2rem 1.5rem;
  }
  
  .impact-icon {
    font-size: 2.5rem;
  }
}
