@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  /* Enhanced Professional Color Palette */
  --primary-color: #1e40af;
  --primary-light: #3b82f6;
  --primary-dark: #1e3a8a;
  --secondary-color: #16a34a;
  --secondary-light: #22c55e;
  --secondary-dark: #15803d;
  --accent-color: #ea580c;
  --accent-light: #f97316;
  --accent-dark: #c2410c;

  /* Professional Text Colors */
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --text-muted: #6b7280;
  --text-light: #9ca3af;
  --text-inverse: #ffffff;

  /* Professional Background Colors */
  --background-primary: #ffffff;
  --background-secondary: #f9fafb;
  --background-tertiary: #f3f4f6;
  --background-dark: #111827;
  --background-card: #ffffff;

  /* Professional Border Colors */
  --border-light: #e5e7eb;
  --border-medium: #d1d5db;
  --border-dark: #9ca3af;

  /* Enhanced Professional Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  /* Professional Gradients */
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --gradient-accent: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
  --gradient-dark: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);

  /* Professional Transitions */
  --transition-fast: 0.15s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-medium: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --transition-slow: 0.5s cubic-bezier(0.4, 0, 0.2, 1);

  /* Professional Spacing */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 2rem;
  --space-2xl: 3rem;
  --space-3xl: 4rem;

  /* Professional Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.7;
  color: var(--text-primary);
  background-color: var(--background-primary);
  overflow-x: hidden;
  font-size: 16px;
  font-weight: 400;
  letter-spacing: -0.01em;
  transition: background-color var(--transition-medium),
              color var(--transition-medium);
}

/* Enhanced theme transition states */
body.theme-transitioning {
  transition: background-color 0.8s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Theme-specific body styles */
[data-theme="dark"] body {
  background-color: var(--background-primary);
  color: var(--text-primary);
}

[data-theme="light"] body {
  background-color: var(--background-primary);
  color: var(--text-primary);
}

/* Theme transition overlay effect - completely removed to prevent text interference */
body.theme-transitioning::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: -1;
  pointer-events: none;
  opacity: 0;
}

@keyframes themeTransitionOverlay {
  0% { opacity: 0; }
  50% { opacity: 0; }
  100% { opacity: 0; }
}

/* Enhanced transitions for common elements - faster and smoother */
h1, h2, h3, h4, h5, h6,
p, span, div, section, article, aside,
nav, header, footer, main {
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure all text elements use theme-appropriate colors */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
}

p, span, div {
  color: var(--text-primary);
}

/* Theme-specific text color overrides */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: var(--text-primary) !important;
}

[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] div {
  color: var(--text-primary) !important;
}

[data-theme="light"] h1,
[data-theme="light"] h2,
[data-theme="light"] h3,
[data-theme="light"] h4,
[data-theme="light"] h5,
[data-theme="light"] h6 {
  color: var(--text-primary) !important;
}

[data-theme="light"] p,
[data-theme="light"] span,
[data-theme="light"] div {
  color: var(--text-primary) !important;
}

/* Ensure proper text visibility for all theme-specific elements */
[data-theme="dark"] .hero-title,
[data-theme="dark"] .hero-subtitle,
[data-theme="dark"] .hero-description {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .about-intro h3,
[data-theme="dark"] .about-intro p {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .philosophy-text h3,
[data-theme="dark"] .philosophy-text p {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .publication-title,
[data-theme="dark"] .publication-authors,
[data-theme="dark"] .publication-year {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .area-card h4,
[data-theme="dark"] .area-card p {
  color: var(--text-primary) !important;
}

/* Fix any remaining opacity issues */
[data-theme="dark"] .stat-label,
[data-theme="dark"] .quote-icon {
  opacity: 1 !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Ensure navigation text is visible */
[data-theme="dark"] .nav-link,
[data-theme="dark"] .mobile-nav-link {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .logo-text {
  color: var(--text-primary) !important;
}

/* Theme-specific page backgrounds */
[data-theme="dark"] .about,
[data-theme="dark"] .research,
[data-theme="dark"] .publications,
[data-theme="dark"] .teaching,
[data-theme="dark"] .contact {
  background: var(--background-primary) !important;
}

[data-theme="light"] .about,
[data-theme="light"] .research,
[data-theme="light"] .publications,
[data-theme="light"] .teaching,
[data-theme="light"] .contact {
  background: var(--background-light) !important;
}

/* Ensure all section backgrounds are theme-aware */
.about,
.research,
.publications,
.teaching,
.contact {
  transition: background-color 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Force text visibility during transitions */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent text from becoming invisible during theme transitions */
body.theme-transitioning * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure all text maintains proper contrast and visibility */
[data-theme="dark"] * {
  text-shadow: none;
  opacity: 1;
}

[data-theme="light"] * {
  text-shadow: none;
  opacity: 1;
}

/* Force immediate text visibility during theme transitions */
body.theme-transitioning h1,
body.theme-transitioning h2,
body.theme-transitioning h3,
body.theme-transitioning h4,
body.theme-transitioning h5,
body.theme-transitioning h6,
body.theme-transitioning p,
body.theme-transitioning span,
body.theme-transitioning div,
body.theme-transitioning a,
body.theme-transitioning li {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
}

/* Fix any remaining text visibility issues */
[data-theme="dark"] .card *,
[data-theme="dark"] .area-card *,
[data-theme="dark"] .philosophy-content *,
[data-theme="dark"] .publication-controls * {
  color: var(--text-primary) !important;
}

[data-theme="light"] .card *,
[data-theme="light"] .area-card *,
[data-theme="light"] .philosophy-content *,
[data-theme="light"] .publication-controls * {
  color: var(--text-primary) !important;
}

/* Ultimate text visibility fix - ensure no text is ever invisible */
[data-theme="dark"] *:not(.btn-primary):not(.footer):not(.page-header):not(.expertise-item):not(.social-link):not(.publication-icon):not(.about-image):not(.hero-image-placeholder) {
  color: var(--text-primary) !important;
  opacity: 1 !important;
}

[data-theme="light"] *:not(.btn-primary):not(.footer):not(.page-header):not(.expertise-item):not(.social-link):not(.publication-icon):not(.about-image):not(.hero-image-placeholder) {
  color: var(--text-primary) !important;
  opacity: 1 !important;
}

/* Specific overrides for elements that should keep their special colors */
[data-theme="dark"] .btn-primary,
[data-theme="dark"] .footer,
[data-theme="dark"] .footer *,
[data-theme="dark"] .page-header,
[data-theme="dark"] .page-header * {
  color: white !important;
}

[data-theme="light"] .btn-primary,
[data-theme="light"] .footer,
[data-theme="light"] .footer *,
[data-theme="light"] .page-header,
[data-theme="light"] .page-header * {
  color: white !important;
}

/* Ensure secondary text elements use appropriate secondary colors */
[data-theme="dark"] .hero-subtitle,
[data-theme="dark"] .publication-journal,
[data-theme="dark"] .area-icon,
[data-theme="dark"] .philosophy-icon {
  color: var(--secondary-color) !important;
}

[data-theme="light"] .hero-subtitle,
[data-theme="light"] .publication-journal,
[data-theme="light"] .area-icon,
[data-theme="light"] .philosophy-icon {
  color: var(--secondary-color) !important;
}

/* Ensure primary colored elements use primary color */
[data-theme="dark"] .hero-title,
[data-theme="dark"] .publication-title,
[data-theme="dark"] .about-intro h3,
[data-theme="dark"] .philosophy-text h3 {
  color: var(--primary-color) !important;
}

[data-theme="light"] .hero-title,
[data-theme="light"] .publication-title,
[data-theme="light"] .about-intro h3,
[data-theme="light"] .philosophy-text h3 {
  color: var(--primary-color) !important;
}

/* Global text visibility enforcement - final safety net */
* {
  opacity: 1;
  visibility: visible;
}

/* Prevent any text from becoming invisible during any state */
h1, h2, h3, h4, h5, h6, p, span, div, a, li, label, input, textarea, button {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Theme transition safety - ensure text is always visible */
body.theme-transitioning *:not(.theme-toggle):not(.toggle-background) {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
}

/* Final safety net - prevent any text from ever becoming invisible */
* {
  min-opacity: 1;
}

/* Ensure proper text rendering during all states */
*:not(.theme-toggle):not(.toggle-background):not(.hero-particles):not(.hero-background):not(.about-image-shimmer) {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Override any potential CSS that might hide text */
[data-theme="dark"] *:not(.btn-primary):not(.footer):not(.page-header):not(.expertise-item):not(.social-link):not(.publication-icon):not(.about-image):not(.hero-image-placeholder):not(.theme-toggle):not(.toggle-background) {
  color: var(--text-primary) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

[data-theme="light"] *:not(.btn-primary):not(.footer):not(.page-header):not(.expertise-item):not(.social-link):not(.publication-icon):not(.about-image):not(.hero-image-placeholder):not(.theme-toggle):not(.toggle-background) {
  color: var(--text-primary) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

html {
  scroll-behavior: smooth;
}

/* Professional Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  line-height: 1.25;
  margin-bottom: var(--space-lg);
  color: var(--text-primary);
  letter-spacing: -0.025em;
}

h1 {
  font-size: clamp(2.5rem, 5vw, 4rem);
  font-weight: 700;
  line-height: 1.1;
  margin-bottom: var(--space-xl);
}

h2 {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: var(--space-lg);
}

h3 {
  font-size: clamp(1.5rem, 3vw, 2.25rem);
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: var(--space-md);
}

h4 {
  font-size: clamp(1.25rem, 2.5vw, 1.875rem);
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: var(--space-md);
}

h5 {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: var(--space-sm);
}

h6 {
  font-size: clamp(1rem, 1.5vw, 1.25rem);
  font-weight: 500;
  line-height: 1.4;
  margin-bottom: var(--space-sm);
}

/* Professional Text Styles */
p {
  margin-bottom: var(--space-lg);
  line-height: 1.75;
  color: var(--text-secondary);
  font-size: 1.125rem;
  font-weight: 400;
  letter-spacing: -0.01em;
}

.text-large {
  font-size: 1.25rem;
  line-height: 1.7;
}

.text-small {
  font-size: 0.875rem;
  line-height: 1.6;
}

.text-xs {
  font-size: 0.75rem;
  line-height: 1.5;
}

.text-muted {
  color: var(--text-muted);
}

.text-light {
  color: var(--text-light);
}

.font-medium {
  font-weight: 500;
}

.font-semibold {
  font-weight: 600;
}

.font-bold {
  font-weight: 700;
}

a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-fast);
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
  transition: var(--transition-fast);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Professional Layout Components */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-xl);
  width: 100%;
}

.container-sm {
  max-width: 640px;
}

.container-md {
  max-width: 768px;
}

.container-lg {
  max-width: 1024px;
}

.container-xl {
  max-width: 1280px;
}

.container-2xl {
  max-width: 1536px;
}

.section {
  padding: var(--space-3xl) 0;
  position: relative;
}

.section-sm {
  padding: var(--space-2xl) 0;
}

.section-lg {
  padding: 6rem 0;
}

.section-xl {
  padding: 8rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: var(--space-3xl);
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -12px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-full);
}

.section-subtitle {
  text-align: center;
  color: var(--text-muted);
  font-size: 1.25rem;
  margin-bottom: var(--space-2xl);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

/* Professional Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--space-sm);
  padding: var(--space-md) var(--space-xl);
  border-radius: var(--radius-lg);
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
  text-align: center;
  text-decoration: none;
  border: 2px solid transparent;
  cursor: pointer;
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
}

.btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--text-inverse);
  border-color: transparent;
  box-shadow: var(--shadow-md);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  filter: brightness(1.1);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background: var(--background-primary);
  color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-outline {
  background: transparent;
  color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: var(--text-inverse);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-ghost {
  background: transparent;
  color: var(--text-secondary);
  border-color: transparent;
}

.btn-ghost:hover {
  background: var(--background-secondary);
  color: var(--text-primary);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-sm) var(--space-lg);
  font-size: 0.875rem;
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--space-lg) var(--space-2xl);
  font-size: 1.125rem;
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--space-xl) var(--space-3xl);
  font-size: 1.25rem;
  border-radius: var(--radius-xl);
}

/* Professional Card Styles */
.card {
  background: var(--background-card);
  border-radius: var(--radius-xl);
  padding: var(--space-2xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-light);
  transition: all var(--transition-medium);
  position: relative;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-medium);
}

.card-sm {
  padding: var(--space-lg);
  border-radius: var(--radius-lg);
}

.card-lg {
  padding: var(--space-3xl);
  border-radius: var(--radius-2xl);
}

.card-elevated {
  box-shadow: var(--shadow-md);
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
}

.card-interactive {
  cursor: pointer;
  transition: all var(--transition-fast);
}

.card-interactive:hover {
  transform: translateY(-2px) scale(1.02);
}

.card-interactive:active {
  transform: translateY(0) scale(1);
}

/* Card Content */
.card-header {
  margin-bottom: var(--space-lg);
  padding-bottom: var(--space-lg);
  border-bottom: 1px solid var(--border-light);
}

.card-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--space-sm);
}

.card-subtitle {
  font-size: 1rem;
  color: var(--text-muted);
  font-weight: 400;
}

.card-body {
  flex: 1;
}

.card-footer {
  margin-top: var(--space-lg);
  padding-top: var(--space-lg);
  border-top: 1px solid var(--border-light);
}

/* Professional Utility Classes */
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-4 {
  gap: var(--space-md);
}

.gap-6 {
  gap: var(--space-lg);
}

.gap-8 {
  gap: var(--space-xl);
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.rounded-lg {
  border-radius: var(--radius-lg);
}

.rounded-xl {
  border-radius: var(--radius-xl);
}

.rounded-full {
  border-radius: var(--radius-full);
}

.shadow-sm {
  box-shadow: var(--shadow-sm);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.transition {
  transition: all var(--transition-medium);
}

.hover\:scale-105:hover {
  transform: scale(1.05);
}

.hover\:shadow-lg:hover {
  box-shadow: var(--shadow-lg);
}

/* Professional Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 var(--space-lg);
  }

  .grid-cols-4 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .container {
    padding: 0 var(--space-md);
  }

  .section {
    padding: var(--space-2xl) 0;
  }

  .section-lg {
    padding: var(--space-3xl) 0;
  }

  .grid-cols-2,
  .grid-cols-3,
  .grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  .btn-lg {
    padding: var(--space-md) var(--space-xl);
    font-size: 1rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 var(--space-md);
  }

  .section {
    padding: var(--space-xl) 0;
  }

  .btn {
    padding: var(--space-sm) var(--space-lg);
    font-size: 0.875rem;
  }

  .card {
    padding: var(--space-lg);
  }
}
