* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

:root {
  --primary-color: #2c3e50;
  --secondary-color: #3498db;
  --accent-color: #e74c3c;
  --text-primary: #2c3e50;
  --text-secondary: #7f8c8d;
  --text-light: #ecf0f1;
  --background-light: #ffffff;
  --background-dark: #f8f9fa;
  --border-color: #e9ecef;
  --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
  --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
  --shadow-heavy: 0 8px 30px rgba(0, 0, 0, 0.2);
  --gradient-primary: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --gradient-secondary: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --transition-fast: 0.2s ease;
  --transition-medium: 0.3s ease;
  --transition-slow: 0.5s ease;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, '<PERSON><PERSON><PERSON> UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', '<PERSON>ra Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--background-light);
  overflow-x: hidden;
  transition: background-color 0.8s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced theme transition states */
body.theme-transitioning {
  transition: background-color 0.8s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Theme-specific body styles */
[data-theme="dark"] body {
  background-color: var(--background-primary);
  color: var(--text-primary);
}

[data-theme="light"] body {
  background-color: var(--background-primary);
  color: var(--text-primary);
}

/* Theme transition overlay effect - completely removed to prevent text interference */
body.theme-transitioning::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: -1;
  pointer-events: none;
  opacity: 0;
}

@keyframes themeTransitionOverlay {
  0% { opacity: 0; }
  50% { opacity: 0; }
  100% { opacity: 0; }
}

/* Enhanced transitions for common elements - faster and smoother */
h1, h2, h3, h4, h5, h6,
p, span, div, section, article, aside,
nav, header, footer, main {
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Ensure all text elements use theme-appropriate colors */
h1, h2, h3, h4, h5, h6 {
  color: var(--text-primary);
}

p, span, div {
  color: var(--text-primary);
}

/* Theme-specific text color overrides */
[data-theme="dark"] h1,
[data-theme="dark"] h2,
[data-theme="dark"] h3,
[data-theme="dark"] h4,
[data-theme="dark"] h5,
[data-theme="dark"] h6 {
  color: var(--text-primary) !important;
}

[data-theme="dark"] p,
[data-theme="dark"] span,
[data-theme="dark"] div {
  color: var(--text-primary) !important;
}

[data-theme="light"] h1,
[data-theme="light"] h2,
[data-theme="light"] h3,
[data-theme="light"] h4,
[data-theme="light"] h5,
[data-theme="light"] h6 {
  color: var(--text-primary) !important;
}

[data-theme="light"] p,
[data-theme="light"] span,
[data-theme="light"] div {
  color: var(--text-primary) !important;
}

/* Ensure proper text visibility for all theme-specific elements */
[data-theme="dark"] .hero-title,
[data-theme="dark"] .hero-subtitle,
[data-theme="dark"] .hero-description {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .about-intro h3,
[data-theme="dark"] .about-intro p {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .philosophy-text h3,
[data-theme="dark"] .philosophy-text p {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .publication-title,
[data-theme="dark"] .publication-authors,
[data-theme="dark"] .publication-year {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .area-card h4,
[data-theme="dark"] .area-card p {
  color: var(--text-primary) !important;
}

/* Fix any remaining opacity issues */
[data-theme="dark"] .stat-label,
[data-theme="dark"] .quote-icon {
  opacity: 1 !important;
  color: rgba(255, 255, 255, 0.9) !important;
}

/* Ensure navigation text is visible */
[data-theme="dark"] .nav-link,
[data-theme="dark"] .mobile-nav-link {
  color: var(--text-primary) !important;
}

[data-theme="dark"] .logo-text {
  color: var(--text-primary) !important;
}

/* Theme-specific page backgrounds */
[data-theme="dark"] .about,
[data-theme="dark"] .research,
[data-theme="dark"] .publications,
[data-theme="dark"] .teaching,
[data-theme="dark"] .contact {
  background: var(--background-primary) !important;
}

[data-theme="light"] .about,
[data-theme="light"] .research,
[data-theme="light"] .publications,
[data-theme="light"] .teaching,
[data-theme="light"] .contact {
  background: var(--background-light) !important;
}

/* Ensure all section backgrounds are theme-aware */
.about,
.research,
.publications,
.teaching,
.contact {
  transition: background-color 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Force text visibility during transitions */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent text from becoming invisible during theme transitions */
body.theme-transitioning * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure all text maintains proper contrast and visibility */
[data-theme="dark"] * {
  text-shadow: none;
  opacity: 1;
}

[data-theme="light"] * {
  text-shadow: none;
  opacity: 1;
}

/* Force immediate text visibility during theme transitions */
body.theme-transitioning h1,
body.theme-transitioning h2,
body.theme-transitioning h3,
body.theme-transitioning h4,
body.theme-transitioning h5,
body.theme-transitioning h6,
body.theme-transitioning p,
body.theme-transitioning span,
body.theme-transitioning div,
body.theme-transitioning a,
body.theme-transitioning li {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
}

/* Fix any remaining text visibility issues */
[data-theme="dark"] .card *,
[data-theme="dark"] .area-card *,
[data-theme="dark"] .philosophy-content *,
[data-theme="dark"] .publication-controls * {
  color: var(--text-primary) !important;
}

[data-theme="light"] .card *,
[data-theme="light"] .area-card *,
[data-theme="light"] .philosophy-content *,
[data-theme="light"] .publication-controls * {
  color: var(--text-primary) !important;
}

/* Ultimate text visibility fix - ensure no text is ever invisible */
[data-theme="dark"] *:not(.btn-primary):not(.footer):not(.page-header):not(.expertise-item):not(.social-link):not(.publication-icon):not(.about-image):not(.hero-image-placeholder) {
  color: var(--text-primary) !important;
  opacity: 1 !important;
}

[data-theme="light"] *:not(.btn-primary):not(.footer):not(.page-header):not(.expertise-item):not(.social-link):not(.publication-icon):not(.about-image):not(.hero-image-placeholder) {
  color: var(--text-primary) !important;
  opacity: 1 !important;
}

/* Specific overrides for elements that should keep their special colors */
[data-theme="dark"] .btn-primary,
[data-theme="dark"] .footer,
[data-theme="dark"] .footer *,
[data-theme="dark"] .page-header,
[data-theme="dark"] .page-header * {
  color: white !important;
}

[data-theme="light"] .btn-primary,
[data-theme="light"] .footer,
[data-theme="light"] .footer *,
[data-theme="light"] .page-header,
[data-theme="light"] .page-header * {
  color: white !important;
}

/* Ensure secondary text elements use appropriate secondary colors */
[data-theme="dark"] .hero-subtitle,
[data-theme="dark"] .publication-journal,
[data-theme="dark"] .area-icon,
[data-theme="dark"] .philosophy-icon {
  color: var(--secondary-color) !important;
}

[data-theme="light"] .hero-subtitle,
[data-theme="light"] .publication-journal,
[data-theme="light"] .area-icon,
[data-theme="light"] .philosophy-icon {
  color: var(--secondary-color) !important;
}

/* Ensure primary colored elements use primary color */
[data-theme="dark"] .hero-title,
[data-theme="dark"] .publication-title,
[data-theme="dark"] .about-intro h3,
[data-theme="dark"] .philosophy-text h3 {
  color: var(--primary-color) !important;
}

[data-theme="light"] .hero-title,
[data-theme="light"] .publication-title,
[data-theme="light"] .about-intro h3,
[data-theme="light"] .philosophy-text h3 {
  color: var(--primary-color) !important;
}

/* Global text visibility enforcement - final safety net */
* {
  opacity: 1;
  visibility: visible;
}

/* Prevent any text from becoming invisible during any state */
h1, h2, h3, h4, h5, h6, p, span, div, a, li, label, input, textarea, button {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Theme transition safety - ensure text is always visible */
body.theme-transitioning *:not(.theme-toggle):not(.toggle-background) {
  opacity: 1 !important;
  visibility: visible !important;
  color: inherit !important;
}

/* Final safety net - prevent any text from ever becoming invisible */
* {
  min-opacity: 1;
}

/* Ensure proper text rendering during all states */
*:not(.theme-toggle):not(.toggle-background):not(.hero-particles):not(.hero-background):not(.about-image-shimmer) {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Override any potential CSS that might hide text */
[data-theme="dark"] *:not(.btn-primary):not(.footer):not(.page-header):not(.expertise-item):not(.social-link):not(.publication-icon):not(.about-image):not(.hero-image-placeholder):not(.theme-toggle):not(.toggle-background) {
  color: var(--text-primary) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

[data-theme="light"] *:not(.btn-primary):not(.footer):not(.page-header):not(.expertise-item):not(.social-link):not(.publication-icon):not(.about-image):not(.hero-image-placeholder):not(.theme-toggle):not(.toggle-background) {
  color: var(--text-primary) !important;
  opacity: 1 !important;
  visibility: visible !important;
}

html {
  scroll-behavior: smooth;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  font-weight: 600;
  line-height: 1.2;
  margin-bottom: 1rem;
}

h1 {
  font-size: 3.5rem;
  font-weight: 700;
}

h2 {
  font-size: 2.5rem;
}

h3 {
  font-size: 1.8rem;
}

h4 {
  font-size: 1.4rem;
}

p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

a {
  text-decoration: none;
  color: inherit;
  transition: var(--transition-fast);
}

button {
  border: none;
  background: none;
  cursor: pointer;
  font-family: inherit;
  transition: var(--transition-fast);
}

img {
  max-width: 100%;
  height: auto;
  display: block;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
}

.section {
  padding: 5rem 0;
}

.section-title {
  text-align: center;
  margin-bottom: 3rem;
  position: relative;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--gradient-primary);
  border-radius: 2px;
}

.btn {
  display: inline-block;
  padding: 12px 30px;
  border-radius: 50px;
  font-weight: 500;
  text-align: center;
  transition: all 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              background 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.6s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  border: 2px solid transparent;
}

.btn-primary {
  background: var(--gradient-primary);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-medium);
}

.btn-outline {
  border: 2px solid var(--primary-color);
  color: var(--primary-color);
}

.btn-outline:hover {
  background: var(--primary-color);
  color: white;
}

.card {
  background: var(--background-primary);
  border-radius: 15px;
  padding: 2rem;
  box-shadow: var(--shadow-light);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--border-color);
}

/* Ensure all card content maintains visibility */
.card * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Dark theme card styles */
[data-theme="dark"] .card {
  background: var(--background-secondary);
  border-color: var(--border-color);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-medium);
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }
  
  .section {
    padding: 3rem 0;
  }
  
  h1 {
    font-size: 2.5rem;
  }
  
  h2 {
    font-size: 2rem;
  }
  
  h3 {
    font-size: 1.5rem;
  }
}

@media (max-width: 480px) {
  h1 {
    font-size: 2rem;
  }
  
  h2 {
    font-size: 1.8rem;
  }
  
  .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }
}
